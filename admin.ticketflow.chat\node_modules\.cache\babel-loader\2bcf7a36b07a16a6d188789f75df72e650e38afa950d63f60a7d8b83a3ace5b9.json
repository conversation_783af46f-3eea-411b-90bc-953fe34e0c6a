{"ast": null, "code": "var _jsxFileName = \"C:\\\\OSPanel\\\\home\\\\admin.ticketflow.chat\\\\src\\\\components\\\\lang-modal.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport { Button, Form, Modal, Select } from 'antd';\nimport { useTranslation } from 'react-i18next';\nimport i18n from '../configs/i18next';\nimport informationService from '../services/rest/information';\nimport { shallowEqual, useDispatch, useSelector } from 'react-redux';\nimport { directionChange } from '../redux/slices/theme';\nimport { setLangugages } from '../redux/slices/formLang';\nimport languagesService from '../services/languages';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport default function LangModal({\n  visible,\n  handleCancel\n}) {\n  _s();\n  const {\n    t\n  } = useTranslation();\n  const [form] = Form.useForm();\n  const [loading, setLoading] = useState(false);\n  const {\n    languages\n  } = useSelector(state => state.formLang, shallowEqual);\n  const dispatch = useDispatch();\n  const isMountedRef = useRef(true);\n  const onFinish = values => {\n    const {\n      lang\n    } = values;\n    const locale = languages.find(item => item.locale === lang);\n    const direction = locale.backward ? 'rtl' : 'ltr';\n    setLoading(true);\n    informationService.translations(values).then(({\n      data\n    }) => {\n      i18n.addResourceBundle(lang, 'translation', data);\n      handleCancel();\n      i18n.changeLanguage(lang);\n      dispatch(directionChange(direction));\n    }).finally(() => setLoading(false));\n  };\n  const fetchLanguages = () => {\n    languagesService.getAllActive().then(({\n      data\n    }) => {\n      dispatch(setLangugages(data));\n    });\n  };\n  useEffect(() => {\n    fetchLanguages();\n  }, []);\n  return /*#__PURE__*/_jsxDEV(Modal, {\n    title: t('change.language'),\n    visible: visible,\n    onCancel: handleCancel,\n    footer: [/*#__PURE__*/_jsxDEV(Button, {\n      type: \"primary\",\n      onClick: () => form.submit(),\n      loading: loading,\n      children: t('save')\n    }, 'ok-button', false, {\n      fileName: _jsxFileName,\n      lineNumber: 51,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Button, {\n      onClick: handleCancel,\n      children: t('cancel')\n    }, 'cancel-button', false, {\n      fileName: _jsxFileName,\n      lineNumber: 59,\n      columnNumber: 9\n    }, this)],\n    children: /*#__PURE__*/_jsxDEV(Form, {\n      layout: \"vertical\",\n      name: \"lang-form\",\n      form: form,\n      onFinish: onFinish,\n      initialValues: {\n        lang: i18n.language\n      },\n      children: /*#__PURE__*/_jsxDEV(Form.Item, {\n        label: t('language'),\n        name: \"lang\",\n        children: /*#__PURE__*/_jsxDEV(Select, {\n          children: languages.map((item, idx) => /*#__PURE__*/_jsxDEV(Select.Option, {\n            value: item.locale,\n            children: item.title\n          }, item.locale + idx, false, {\n            fileName: _jsxFileName,\n            lineNumber: 74,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 72,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 71,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 64,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 46,\n    columnNumber: 5\n  }, this);\n}\n_s(LangModal, \"lm2uosB2SBA02DLKgTFVJxdHjtA=\", false, function () {\n  return [useTranslation, Form.useForm, useSelector, useDispatch];\n});\n_c = LangModal;\nvar _c;\n$RefreshReg$(_c, \"LangModal\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "<PERSON><PERSON>", "Form", "Modal", "Select", "useTranslation", "i18n", "informationService", "shallowEqual", "useDispatch", "useSelector", "directionChange", "setLangugages", "languagesService", "jsxDEV", "_jsxDEV", "LangModal", "visible", "handleCancel", "_s", "t", "form", "useForm", "loading", "setLoading", "languages", "state", "formLang", "dispatch", "isMountedRef", "onFinish", "values", "lang", "locale", "find", "item", "direction", "backward", "translations", "then", "data", "addResourceBundle", "changeLanguage", "finally", "fetchLanguages", "getAllActive", "title", "onCancel", "footer", "type", "onClick", "submit", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "layout", "name", "initialValues", "language", "<PERSON><PERSON>", "label", "map", "idx", "Option", "value", "_c", "$RefreshReg$"], "sources": ["C:/OSPanel/home/<USER>/src/components/lang-modal.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport { Button, Form, Modal, Select } from 'antd';\nimport { useTranslation } from 'react-i18next';\nimport i18n from '../configs/i18next';\nimport informationService from '../services/rest/information';\nimport { shallowEqual, useDispatch, useSelector } from 'react-redux';\nimport { directionChange } from '../redux/slices/theme';\nimport { setLangugages } from '../redux/slices/formLang';\nimport languagesService from '../services/languages';\n\nexport default function LangModal({ visible, handleCancel }) {\n  const { t } = useTranslation();\n  const [form] = Form.useForm();\n  const [loading, setLoading] = useState(false);\n  const { languages } = useSelector((state) => state.formLang, shallowEqual);\n  const dispatch = useDispatch();\n  const isMountedRef = useRef(true);\n\n  const onFinish = (values) => {\n    const { lang } = values;\n    const locale = languages.find((item) => item.locale === lang);\n    const direction = locale.backward ? 'rtl' : 'ltr';\n    setLoading(true);\n    informationService\n      .translations(values)\n      .then(({ data }) => {\n        i18n.addResourceBundle(lang, 'translation', data);\n        handleCancel();\n        i18n.changeLanguage(lang);\n        dispatch(directionChange(direction));\n      })\n      .finally(() => setLoading(false));\n  };\n\n  const fetchLanguages = () => {\n    languagesService.getAllActive().then(({ data }) => {\n      dispatch(setLangugages(data));\n    });\n  };\n\n  useEffect(() => {\n    fetchLanguages();\n  }, []);\n\n  return (\n    <Modal\n      title={t('change.language')}\n      visible={visible}\n      onCancel={handleCancel}\n      footer={[\n        <Button\n          key='ok-button'\n          type='primary'\n          onClick={() => form.submit()}\n          loading={loading}\n        >\n          {t('save')}\n        </Button>,\n        <Button key='cancel-button' onClick={handleCancel}>\n          {t('cancel')}\n        </Button>,\n      ]}\n    >\n      <Form\n        layout='vertical'\n        name='lang-form'\n        form={form}\n        onFinish={onFinish}\n        initialValues={{ lang: i18n.language }}\n      >\n        <Form.Item label={t('language')} name='lang'>\n          <Select>\n            {languages.map((item, idx) => (\n              <Select.Option key={item.locale + idx} value={item.locale}>\n                {item.title}\n              </Select.Option>\n            ))}\n          </Select>\n        </Form.Item>\n      </Form>\n    </Modal>\n  );\n}\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,MAAM,EAAEC,IAAI,EAAEC,KAAK,EAAEC,MAAM,QAAQ,MAAM;AAClD,SAASC,cAAc,QAAQ,eAAe;AAC9C,OAAOC,IAAI,MAAM,oBAAoB;AACrC,OAAOC,kBAAkB,MAAM,8BAA8B;AAC7D,SAASC,YAAY,EAAEC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACpE,SAASC,eAAe,QAAQ,uBAAuB;AACvD,SAASC,aAAa,QAAQ,0BAA0B;AACxD,OAAOC,gBAAgB,MAAM,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErD,eAAe,SAASC,SAASA,CAAC;EAAEC,OAAO;EAAEC;AAAa,CAAC,EAAE;EAAAC,EAAA;EAC3D,MAAM;IAAEC;EAAE,CAAC,GAAGf,cAAc,CAAC,CAAC;EAC9B,MAAM,CAACgB,IAAI,CAAC,GAAGnB,IAAI,CAACoB,OAAO,CAAC,CAAC;EAC7B,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM;IAAE2B;EAAU,CAAC,GAAGf,WAAW,CAAEgB,KAAK,IAAKA,KAAK,CAACC,QAAQ,EAAEnB,YAAY,CAAC;EAC1E,MAAMoB,QAAQ,GAAGnB,WAAW,CAAC,CAAC;EAC9B,MAAMoB,YAAY,GAAG7B,MAAM,CAAC,IAAI,CAAC;EAEjC,MAAM8B,QAAQ,GAAIC,MAAM,IAAK;IAC3B,MAAM;MAAEC;IAAK,CAAC,GAAGD,MAAM;IACvB,MAAME,MAAM,GAAGR,SAAS,CAACS,IAAI,CAAEC,IAAI,IAAKA,IAAI,CAACF,MAAM,KAAKD,IAAI,CAAC;IAC7D,MAAMI,SAAS,GAAGH,MAAM,CAACI,QAAQ,GAAG,KAAK,GAAG,KAAK;IACjDb,UAAU,CAAC,IAAI,CAAC;IAChBjB,kBAAkB,CACf+B,YAAY,CAACP,MAAM,CAAC,CACpBQ,IAAI,CAAC,CAAC;MAAEC;IAAK,CAAC,KAAK;MAClBlC,IAAI,CAACmC,iBAAiB,CAACT,IAAI,EAAE,aAAa,EAAEQ,IAAI,CAAC;MACjDtB,YAAY,CAAC,CAAC;MACdZ,IAAI,CAACoC,cAAc,CAACV,IAAI,CAAC;MACzBJ,QAAQ,CAACjB,eAAe,CAACyB,SAAS,CAAC,CAAC;IACtC,CAAC,CAAC,CACDO,OAAO,CAAC,MAAMnB,UAAU,CAAC,KAAK,CAAC,CAAC;EACrC,CAAC;EAED,MAAMoB,cAAc,GAAGA,CAAA,KAAM;IAC3B/B,gBAAgB,CAACgC,YAAY,CAAC,CAAC,CAACN,IAAI,CAAC,CAAC;MAAEC;IAAK,CAAC,KAAK;MACjDZ,QAAQ,CAAChB,aAAa,CAAC4B,IAAI,CAAC,CAAC;IAC/B,CAAC,CAAC;EACJ,CAAC;EAEDzC,SAAS,CAAC,MAAM;IACd6C,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,EAAE,CAAC;EAEN,oBACE7B,OAAA,CAACZ,KAAK;IACJ2C,KAAK,EAAE1B,CAAC,CAAC,iBAAiB,CAAE;IAC5BH,OAAO,EAAEA,OAAQ;IACjB8B,QAAQ,EAAE7B,YAAa;IACvB8B,MAAM,EAAE,cACNjC,OAAA,CAACd,MAAM;MAELgD,IAAI,EAAC,SAAS;MACdC,OAAO,EAAEA,CAAA,KAAM7B,IAAI,CAAC8B,MAAM,CAAC,CAAE;MAC7B5B,OAAO,EAAEA,OAAQ;MAAA6B,QAAA,EAEhBhC,CAAC,CAAC,MAAM;IAAC,GALN,WAAW;MAAAiC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAMT,CAAC,eACTzC,OAAA,CAACd,MAAM;MAAqBiD,OAAO,EAAEhC,YAAa;MAAAkC,QAAA,EAC/ChC,CAAC,CAAC,QAAQ;IAAC,GADF,eAAe;MAAAiC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEnB,CAAC,CACT;IAAAJ,QAAA,eAEFrC,OAAA,CAACb,IAAI;MACHuD,MAAM,EAAC,UAAU;MACjBC,IAAI,EAAC,WAAW;MAChBrC,IAAI,EAAEA,IAAK;MACXS,QAAQ,EAAEA,QAAS;MACnB6B,aAAa,EAAE;QAAE3B,IAAI,EAAE1B,IAAI,CAACsD;MAAS,CAAE;MAAAR,QAAA,eAEvCrC,OAAA,CAACb,IAAI,CAAC2D,IAAI;QAACC,KAAK,EAAE1C,CAAC,CAAC,UAAU,CAAE;QAACsC,IAAI,EAAC,MAAM;QAAAN,QAAA,eAC1CrC,OAAA,CAACX,MAAM;UAAAgD,QAAA,EACJ3B,SAAS,CAACsC,GAAG,CAAC,CAAC5B,IAAI,EAAE6B,GAAG,kBACvBjD,OAAA,CAACX,MAAM,CAAC6D,MAAM;YAAyBC,KAAK,EAAE/B,IAAI,CAACF,MAAO;YAAAmB,QAAA,EACvDjB,IAAI,CAACW;UAAK,GADOX,IAAI,CAACF,MAAM,GAAG+B,GAAG;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEtB,CAChB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEZ;AAACrC,EAAA,CAxEuBH,SAAS;EAAA,QACjBX,cAAc,EACbH,IAAI,CAACoB,OAAO,EAELZ,WAAW,EAChBD,WAAW;AAAA;AAAA0D,EAAA,GALNnD,SAAS;AAAA,IAAAmD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}