{"ast": null, "code": "var _jsxFileName = \"C:\\\\OSPanel\\\\home\\\\admin.ticketflow.chat\\\\src\\\\components\\\\sidebar.js\",\n  _s = $RefreshSig$();\nimport React, { useMemo, useState, useRef, useEffect } from 'react';\nimport { Link, useLocation, useNavigate } from 'react-router-dom';\nimport { LogoutOutlined, MenuFoldOutlined, MenuUnfoldOutlined, SearchOutlined } from '@ant-design/icons';\nimport { Divider, Menu, Space, Layout, Modal, Input } from 'antd';\nimport { shallowEqual, useDispatch, useSelector } from 'react-redux';\nimport { addMenu, clearMenu, setMenu } from '../redux/slices/menu';\nimport { useTranslation } from 'react-i18next';\nimport LangModal from './lang-modal';\nimport getSystemIcons from '../helpers/getSystemIcons';\nimport NotificationBar from './notificationBar';\nimport { navCollapseTrigger } from '../redux/slices/theme';\nimport ThemeConfigurator from './theme-configurator';\nimport i18n from '../configs/i18next';\nimport { RiArrowDownSFill } from 'react-icons/ri';\nimport Scrollbars from 'react-custom-scrollbars';\nimport SubMenu from 'antd/lib/menu/SubMenu';\nimport NavProfile from './nav-profile';\nimport { batch } from 'react-redux';\nimport { clearUser } from '../redux/slices/auth';\nimport { setCurrentChat } from '../redux/slices/chat';\nimport { data as allRoutes } from 'configs/menu-config';\nimport useDidUpdate from 'helpers/useDidUpdate';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst {\n  Sider\n} = Layout;\nconst Sidebar = () => {\n  _s();\n  var _languages$find;\n  const {\n    t\n  } = useTranslation();\n  const navigate = useNavigate();\n  const {\n    pathname\n  } = useLocation();\n  const {\n    user\n  } = useSelector(state => state.auth, shallowEqual);\n  const [isModalVisible, setIsModalVisible] = useState(false);\n  const isMountedRef = useRef(true);\n  const {\n    system_refund,\n    payment_type,\n    by_subscription\n  } = useSelector(state => state.globalSettings.settings, shallowEqual);\n  const {\n    navCollapsed\n  } = useSelector(state => state.theme.theme, shallowEqual);\n  const {\n    languages\n  } = useSelector(state => state.formLang, shallowEqual);\n  const dispatch = useDispatch();\n  const [langModal, setLangModal] = useState(false);\n  const {\n    myShop\n  } = useSelector(state => state.myShop, shallowEqual);\n  const {\n    theme\n  } = useSelector(state => state.theme, shallowEqual);\n  const parcelMode = useMemo(() => !!theme.parcelMode && (user === null || user === void 0 ? void 0 : user.role) === 'admin', [theme, user]);\n  const routes = useMemo(() => {\n    var _user$urls;\n    const isSubscriptionEnabled = by_subscription === '1';\n    const excludeRoutes = {\n      routes: [],\n      subRoutes: []\n    };\n    if (!isSubscriptionEnabled) {\n      excludeRoutes.subRoutes.push('subscriptions', 'my.subscriptions', 'shop.subscriptions');\n    }\n    return filterUserRoutes((_user$urls = user.urls) === null || _user$urls === void 0 ? void 0 : _user$urls.filter(item => !excludeRoutes.routes.includes(item.name)).map(item => {\n      var _item$submenu;\n      return {\n        ...item,\n        submenu: (_item$submenu = item.submenu) === null || _item$submenu === void 0 ? void 0 : _item$submenu.filter(sub => !excludeRoutes.subRoutes.includes(sub.name))\n      };\n    }));\n  }, [user]);\n  const active = routes === null || routes === void 0 ? void 0 : routes.find(item => pathname.includes(item.url));\n  const [searchTerm, setSearchTerm] = useState('');\n  const [data, setData] = useState(parcelMode ? allRoutes.parcel : routes);\n  useDidUpdate(() => {\n    if (parcelMode) {\n      setData(allRoutes.parcel);\n    } else {\n      setData(routes);\n    }\n  }, [theme, user]);\n  const addNewItem = item => {\n    if (typeof item.url === 'undefined') return;\n    if (item.name === 'logout') {\n      setIsModalVisible(true);\n      return;\n    }\n    const data = {\n      ...item,\n      icon: undefined,\n      children: undefined,\n      refetch: true\n    };\n    dispatch(setMenu(data));\n    navigate(`/${item.url}`);\n  };\n  function filterUserRoutes(routes) {\n    let list = routes;\n    if (myShop.type === 'shop') {\n      list = routes === null || routes === void 0 ? void 0 : routes.filter(item => (item === null || item === void 0 ? void 0 : item.name) !== 'brands');\n    }\n    if (payment_type === 'admin') {\n      list = routes === null || routes === void 0 ? void 0 : routes.filter(item => (item === null || item === void 0 ? void 0 : item.name) !== 'payments');\n    }\n    if (system_refund === '0') {\n      list = routes === null || routes === void 0 ? void 0 : routes.filter(item => (item === null || item === void 0 ? void 0 : item.name) !== 'refunds');\n    }\n    return list;\n  }\n  const menuTrigger = event => {\n    event.stopPropagation();\n    dispatch(navCollapseTrigger());\n  };\n  const addMenuItem = payload => {\n    const data = {\n      ...payload,\n      icon: undefined\n    };\n    dispatch(addMenu(data));\n  };\n  const handleOk = () => {\n    batch(() => {\n      dispatch(clearUser());\n      dispatch(clearMenu());\n      dispatch(setCurrentChat(null));\n    });\n    setIsModalVisible(false);\n    localStorage.removeItem('token');\n    navigate('/login');\n  };\n  const handleCancel = () => setIsModalVisible(false);\n  function getOptionList(routes) {\n    const optionTree = [];\n    routes === null || routes === void 0 ? void 0 : routes.map(item => {\n      var _item$submenu2;\n      optionTree.push(item);\n      item === null || item === void 0 ? void 0 : (_item$submenu2 = item.submenu) === null || _item$submenu2 === void 0 ? void 0 : _item$submenu2.map(sub => {\n        var _sub$children;\n        optionTree.push(sub);\n        sub === null || sub === void 0 ? void 0 : (_sub$children = sub.children) === null || _sub$children === void 0 ? void 0 : _sub$children.map(child => {\n          optionTree.push(child);\n        });\n      });\n    });\n    return optionTree;\n  }\n  const optionList = getOptionList(data);\n  const menuList = searchTerm.length > 0 ? optionList.filter(input => {\n    var _input$name;\n    return t((_input$name = input === null || input === void 0 ? void 0 : input.name) !== null && _input$name !== void 0 ? _input$name : '').toUpperCase().includes(searchTerm.toUpperCase());\n  }) : data;\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(Sider, {\n      className: \"navbar-nav side-nav\",\n      width: 250,\n      collapsed: navCollapsed,\n      style: {\n        height: '100vh',\n        top: 0\n      },\n      children: [/*#__PURE__*/_jsxDEV(NavProfile, {\n        user: user\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 173,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"menu-collapse\",\n        onClick: menuTrigger,\n        children: /*#__PURE__*/_jsxDEV(MenuFoldOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 175,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 174,\n        columnNumber: 9\n      }, this), navCollapsed && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-content-center\",\n        children: /*#__PURE__*/_jsxDEV(ThemeConfigurator, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 179,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 178,\n        columnNumber: 11\n      }, this), !navCollapsed ? /*#__PURE__*/_jsxDEV(Space, {\n        className: \"mx-4 mt-2 d-flex justify-content-between\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"icon-button\",\n          onClick: () => setLangModal(true),\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            className: \"globalOutlined\",\n            src: (_languages$find = languages.find(item => item.locale === i18n.language)) === null || _languages$find === void 0 ? void 0 : _languages$find.img,\n            alt: user.fullName\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"default-lang\",\n            children: i18n.language\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 193,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(RiArrowDownSFill, {\n            size: 15\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 194,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"d-flex\",\n          children: [/*#__PURE__*/_jsxDEV(ThemeConfigurator, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 197,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(NotificationBar, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 198,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 196,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 184,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"menu-unfold\",\n        onClick: menuTrigger,\n        children: /*#__PURE__*/_jsxDEV(MenuUnfoldOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 203,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 202,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Divider, {\n        style: {\n          margin: '10px 0'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 206,\n        columnNumber: 9\n      }, this), !navCollapsed && /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"mt-2 mb-2 d-flex justify-content-center\",\n        children: /*#__PURE__*/_jsxDEV(Input, {\n          placeholder: \"search\",\n          style: {\n            width: '90%'\n          },\n          value: searchTerm,\n          onChange: event => {\n            setSearchTerm(event.target.value);\n          },\n          prefix: /*#__PURE__*/_jsxDEV(SearchOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 217,\n            columnNumber: 23\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 210,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 209,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Scrollbars, {\n        autoHeight: true,\n        autoHeightMin: window.innerHeight > 969 ? '80vh' : '77vh',\n        autoHeightMax: window.innerHeight > 969 ? '80vh' : '77vh',\n        autoHide: true,\n        children: /*#__PURE__*/_jsxDEV(Menu, {\n          theme: \"light\",\n          mode: \"inline\",\n          defaultSelectedKeys: [String(active === null || active === void 0 ? void 0 : active.id)],\n          defaultOpenKeys: !navCollapsed ? data === null || data === void 0 ? void 0 : data.map((i, idx) => i.id + '_' + idx) : [],\n          children: menuList === null || menuList === void 0 ? void 0 : menuList.map((item, idx) => {\n            var _item$submenu3;\n            return ((_item$submenu3 = item.submenu) === null || _item$submenu3 === void 0 ? void 0 : _item$submenu3.length) > 0 ? /*#__PURE__*/_jsxDEV(SubMenu, {\n              title: t(item.name),\n              icon: getSystemIcons(item.icon),\n              children: item.submenu.map((submenu, idy) => {\n                var _submenu$children, _submenu$children2;\n                return ((_submenu$children = submenu.children) === null || _submenu$children === void 0 ? void 0 : _submenu$children.length) > 0 ? /*#__PURE__*/_jsxDEV(SubMenu, {\n                  defaultOpen: true,\n                  title: t(submenu.name),\n                  icon: getSystemIcons(submenu.icon),\n                  onTitleClick: () => addNewItem(submenu),\n                  children: (_submenu$children2 = submenu.children) === null || _submenu$children2 === void 0 ? void 0 : _submenu$children2.map((sub, idk) => /*#__PURE__*/_jsxDEV(Menu.Item, {\n                    icon: getSystemIcons(sub.icon),\n                    children: /*#__PURE__*/_jsxDEV(Link, {\n                      to: '/' + sub.url,\n                      onClick: () => addMenuItem(sub),\n                      children: /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: t(sub.name)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 261,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 257,\n                      columnNumber: 29\n                    }, this)\n                  }, 'child' + idk + sub.id, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 253,\n                    columnNumber: 27\n                  }, this))\n                }, submenu.id + '_' + idy, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 245,\n                  columnNumber: 23\n                }, this) : /*#__PURE__*/_jsxDEV(Menu.Item, {\n                  icon: getSystemIcons(submenu.icon),\n                  children: /*#__PURE__*/_jsxDEV(Link, {\n                    to: '/' + submenu.url,\n                    onClick: () => addNewItem(submenu),\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: t(submenu.name)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 275,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 271,\n                    columnNumber: 25\n                  }, this)\n                }, submenu.id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 267,\n                  columnNumber: 23\n                }, this);\n              })\n            }, item.id + '_' + idx, false, {\n              fileName: _jsxFileName,\n              lineNumber: 238,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(Menu.Item, {\n              icon: getSystemIcons(item.icon),\n              children: /*#__PURE__*/_jsxDEV(Link, {\n                to: '/' + item.url,\n                onClick: () => addNewItem(item),\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: t(item.name)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 284,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 283,\n                columnNumber: 19\n              }, this)\n            }, item.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 282,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 228,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 222,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 167,\n      columnNumber: 7\n    }, this), langModal && /*#__PURE__*/_jsxDEV(LangModal, {\n      visible: langModal,\n      handleCancel: () => setLangModal(false)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 294,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      visible: isModalVisible,\n      onOk: handleOk,\n      onCancel: handleCancel,\n      centered: true,\n      children: [/*#__PURE__*/_jsxDEV(LogoutOutlined, {\n        style: {\n          fontSize: '25px',\n          color: '#08c'\n        },\n        theme: \"primary\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 306,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"ml-2\",\n        children: t('leave.site')\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 310,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 300,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(Sidebar, \"vBr8lJHFK2+jowrZfAJFJnH4bmw=\", false, function () {\n  return [useTranslation, useNavigate, useLocation, useSelector, useSelector, useSelector, useSelector, useDispatch, useSelector, useSelector, useDidUpdate];\n});\n_c = Sidebar;\nexport default Sidebar;\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");", "map": {"version": 3, "names": ["React", "useMemo", "useState", "useRef", "useEffect", "Link", "useLocation", "useNavigate", "LogoutOutlined", "MenuFoldOutlined", "MenuUnfoldOutlined", "SearchOutlined", "Divider", "<PERSON><PERSON>", "Space", "Layout", "Modal", "Input", "shallowEqual", "useDispatch", "useSelector", "addMenu", "clearMenu", "setMenu", "useTranslation", "LangModal", "getSystemIcons", "NotificationBar", "navCollapseTrigger", "ThemeConfigurator", "i18n", "RiArrowDownSFill", "Scrollbars", "SubMenu", "NavProfile", "batch", "clearUser", "setCurrentChat", "data", "allRoutes", "useDidUpdate", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "<PERSON><PERSON>", "Sidebar", "_s", "_languages$find", "t", "navigate", "pathname", "user", "state", "auth", "isModalVisible", "setIsModalVisible", "isMountedRef", "system_refund", "payment_type", "by_subscription", "globalSettings", "settings", "navCollapsed", "theme", "languages", "formLang", "dispatch", "langModal", "setLangModal", "myShop", "parcelMode", "role", "routes", "_user$urls", "isSubscriptionEnabled", "excludeRoutes", "subRoutes", "push", "filterUserRoutes", "urls", "filter", "item", "includes", "name", "map", "_item$submenu", "submenu", "sub", "active", "find", "url", "searchTerm", "setSearchTerm", "setData", "parcel", "addNewItem", "icon", "undefined", "children", "refetch", "list", "type", "menuTrigger", "event", "stopPropagation", "addMenuItem", "payload", "handleOk", "localStorage", "removeItem", "handleCancel", "getOptionList", "optionTree", "_item$submenu2", "_sub$children", "child", "optionList", "menuList", "length", "input", "_input$name", "toUpperCase", "className", "width", "collapsed", "style", "height", "top", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "src", "locale", "language", "img", "alt", "fullName", "size", "margin", "placeholder", "value", "onChange", "target", "prefix", "autoHeight", "autoHeightMin", "window", "innerHeight", "autoHeightMax", "autoHide", "mode", "defaultSelectedKeys", "String", "id", "defaultOpenKeys", "i", "idx", "_item$submenu3", "title", "idy", "_submenu$children", "_submenu$children2", "defaultOpen", "onTitleClick", "idk", "<PERSON><PERSON>", "to", "visible", "onOk", "onCancel", "centered", "fontSize", "color", "_c", "$RefreshReg$"], "sources": ["C:/OSPanel/home/<USER>/src/components/sidebar.js"], "sourcesContent": ["import React, { useMemo, useState, useRef, useEffect } from 'react';\nimport { Link, useLocation, useNavigate } from 'react-router-dom';\nimport {\n  LogoutOutlined,\n  MenuFoldOutlined,\n  MenuUnfoldOutlined,\n  SearchOutlined,\n} from '@ant-design/icons';\nimport { Divider, Menu, Space, Layout, Modal, Input } from 'antd';\nimport { shallowEqual, useDispatch, useSelector } from 'react-redux';\nimport { addMenu, clearMenu, setMenu } from '../redux/slices/menu';\nimport { useTranslation } from 'react-i18next';\nimport LangModal from './lang-modal';\nimport getSystemIcons from '../helpers/getSystemIcons';\nimport NotificationBar from './notificationBar';\nimport { navCollapseTrigger } from '../redux/slices/theme';\nimport ThemeConfigurator from './theme-configurator';\nimport i18n from '../configs/i18next';\nimport { RiArrowDownSFill } from 'react-icons/ri';\nimport Scrollbars from 'react-custom-scrollbars';\nimport SubMenu from 'antd/lib/menu/SubMenu';\nimport NavProfile from './nav-profile';\nimport { batch } from 'react-redux';\nimport { clearUser } from '../redux/slices/auth';\nimport { setCurrentChat } from '../redux/slices/chat';\nimport { data as allRoutes } from 'configs/menu-config';\nimport useDidUpdate from 'helpers/useDidUpdate';\nconst { Sider } = Layout;\n\nconst Sidebar = () => {\n  const { t } = useTranslation();\n  const navigate = useNavigate();\n  const { pathname } = useLocation();\n  const { user } = useSelector((state) => state.auth, shallowEqual);\n  const [isModalVisible, setIsModalVisible] = useState(false);\n  const isMountedRef = useRef(true);\n  const { system_refund, payment_type, by_subscription } = useSelector(\n    (state) => state.globalSettings.settings,\n    shallowEqual,\n  );\n  const { navCollapsed } = useSelector(\n    (state) => state.theme.theme,\n    shallowEqual,\n  );\n  const { languages } = useSelector((state) => state.formLang, shallowEqual);\n  const dispatch = useDispatch();\n  const [langModal, setLangModal] = useState(false);\n  const { myShop } = useSelector((state) => state.myShop, shallowEqual);\n  const { theme } = useSelector((state) => state.theme, shallowEqual);\n  const parcelMode = useMemo(\n    () => !!theme.parcelMode && user?.role === 'admin',\n    [theme, user],\n  );\n  const routes = useMemo(() => {\n    const isSubscriptionEnabled = by_subscription === '1';\n    const excludeRoutes = { routes: [], subRoutes: [] };\n    if (!isSubscriptionEnabled) {\n      excludeRoutes.subRoutes.push(\n        'subscriptions',\n        'my.subscriptions',\n        'shop.subscriptions',\n      );\n    }\n    return filterUserRoutes(\n      user.urls\n        ?.filter((item) => !excludeRoutes.routes.includes(item.name))\n        .map((item) => ({\n          ...item,\n          submenu: item.submenu?.filter(\n            (sub) => !excludeRoutes.subRoutes.includes(sub.name),\n          ),\n        })),\n    );\n  }, [user]);\n  const active = routes?.find((item) => pathname.includes(item.url));\n  const [searchTerm, setSearchTerm] = useState('');\n  const [data, setData] = useState(parcelMode ? allRoutes.parcel : routes);\n\n  useDidUpdate(() => {\n    if (parcelMode) {\n      setData(allRoutes.parcel);\n    } else {\n      setData(routes);\n    }\n  }, [theme, user]);\n\n  const addNewItem = (item) => {\n    if (typeof item.url === 'undefined') return;\n    if (item.name === 'logout') {\n      setIsModalVisible(true);\n      return;\n    }\n    const data = {\n      ...item,\n      icon: undefined,\n      children: undefined,\n      refetch: true,\n    };\n    dispatch(setMenu(data));\n    navigate(`/${item.url}`);\n  };\n\n  function filterUserRoutes(routes) {\n    let list = routes;\n    if (myShop.type === 'shop') {\n      list = routes?.filter((item) => item?.name !== 'brands');\n    }\n    if (payment_type === 'admin') {\n      list = routes?.filter((item) => item?.name !== 'payments');\n    }\n    if (system_refund === '0') {\n      list = routes?.filter((item) => item?.name !== 'refunds');\n    }\n    return list;\n  }\n\n  const menuTrigger = (event) => {\n    event.stopPropagation();\n    dispatch(navCollapseTrigger());\n  };\n\n  const addMenuItem = (payload) => {\n    const data = { ...payload, icon: undefined };\n    dispatch(addMenu(data));\n  };\n\n  const handleOk = () => {\n    batch(() => {\n      dispatch(clearUser());\n      dispatch(clearMenu());\n      dispatch(setCurrentChat(null));\n    });\n    setIsModalVisible(false);\n    localStorage.removeItem('token');\n    navigate('/login');\n  };\n\n  const handleCancel = () => setIsModalVisible(false);\n\n  function getOptionList(routes) {\n    const optionTree = [];\n    routes?.map((item) => {\n      optionTree.push(item);\n      item?.submenu?.map((sub) => {\n        optionTree.push(sub);\n        sub?.children?.map((child) => {\n          optionTree.push(child);\n        });\n      });\n    });\n    return optionTree;\n  }\n\n  const optionList = getOptionList(data);\n\n  const menuList =\n    searchTerm.length > 0\n      ? optionList.filter((input) =>\n          t(input?.name ?? '')\n            .toUpperCase()\n            .includes(searchTerm.toUpperCase()),\n        )\n      : data;\n\n  return (\n    <>\n      <Sider\n        className='navbar-nav side-nav'\n        width={250}\n        collapsed={navCollapsed}\n        style={{ height: '100vh', top: 0 }}\n      >\n        <NavProfile user={user} />\n        <div className='menu-collapse' onClick={menuTrigger}>\n          <MenuFoldOutlined />\n        </div>\n        {navCollapsed && (\n          <div className='flex justify-content-center'>\n            <ThemeConfigurator />\n          </div>\n        )}\n\n        {!navCollapsed ? (\n          <Space className='mx-4 mt-2 d-flex justify-content-between'>\n            <span className='icon-button' onClick={() => setLangModal(true)}>\n              <img\n                className='globalOutlined'\n                src={\n                  languages.find((item) => item.locale === i18n.language)?.img\n                }\n                alt={user.fullName}\n              />\n              <span className='default-lang'>{i18n.language}</span>\n              <RiArrowDownSFill size={15} />\n            </span>\n            <span className='d-flex'>\n              <ThemeConfigurator />\n              <NotificationBar />\n            </span>\n          </Space>\n        ) : (\n          <div className='menu-unfold' onClick={menuTrigger}>\n            <MenuUnfoldOutlined />\n          </div>\n        )}\n        <Divider style={{ margin: '10px 0' }} />\n\n        {!navCollapsed && (\n          <span className='mt-2 mb-2 d-flex justify-content-center'>\n            <Input\n              placeholder='search'\n              style={{ width: '90%' }}\n              value={searchTerm}\n              onChange={(event) => {\n                setSearchTerm(event.target.value);\n              }}\n              prefix={<SearchOutlined />}\n            />\n          </span>\n        )}\n\n        <Scrollbars\n          autoHeight\n          autoHeightMin={window.innerHeight > 969 ? '80vh' : '77vh'}\n          autoHeightMax={window.innerHeight > 969 ? '80vh' : '77vh'}\n          autoHide\n        >\n          <Menu\n            theme='light'\n            mode='inline'\n            defaultSelectedKeys={[String(active?.id)]}\n            defaultOpenKeys={\n              !navCollapsed ? data?.map((i, idx) => i.id + '_' + idx) : []\n            }\n          >\n            {menuList?.map((item, idx) =>\n              item.submenu?.length > 0 ? (\n                <SubMenu\n                  key={item.id + '_' + idx}\n                  title={t(item.name)}\n                  icon={getSystemIcons(item.icon)}\n                >\n                  {item.submenu.map((submenu, idy) =>\n                    submenu.children?.length > 0 ? (\n                      <SubMenu\n                        defaultOpen={true}\n                        key={submenu.id + '_' + idy}\n                        title={t(submenu.name)}\n                        icon={getSystemIcons(submenu.icon)}\n                        onTitleClick={() => addNewItem(submenu)}\n                      >\n                        {submenu.children?.map((sub, idk) => (\n                          <Menu.Item\n                            key={'child' + idk + sub.id}\n                            icon={getSystemIcons(sub.icon)}\n                          >\n                            <Link\n                              to={'/' + sub.url}\n                              onClick={() => addMenuItem(sub)}\n                            >\n                              <span>{t(sub.name)}</span>\n                            </Link>\n                          </Menu.Item>\n                        ))}\n                      </SubMenu>\n                    ) : (\n                      <Menu.Item\n                        key={submenu.id}\n                        icon={getSystemIcons(submenu.icon)}\n                      >\n                        <Link\n                          to={'/' + submenu.url}\n                          onClick={() => addNewItem(submenu)}\n                        >\n                          <span>{t(submenu.name)}</span>\n                        </Link>\n                      </Menu.Item>\n                    ),\n                  )}\n                </SubMenu>\n              ) : (\n                <Menu.Item key={item.id} icon={getSystemIcons(item.icon)}>\n                  <Link to={'/' + item.url} onClick={() => addNewItem(item)}>\n                    <span>{t(item.name)}</span>\n                  </Link>\n                </Menu.Item>\n              ),\n            )}\n          </Menu>\n        </Scrollbars>\n      </Sider>\n\n      {langModal && (\n        <LangModal\n          visible={langModal}\n          handleCancel={() => setLangModal(false)}\n        />\n      )}\n\n      <Modal\n        visible={isModalVisible}\n        onOk={handleOk}\n        onCancel={handleCancel}\n        centered\n      >\n        <LogoutOutlined\n          style={{ fontSize: '25px', color: '#08c' }}\n          theme='primary'\n        />\n        <span className='ml-2'>{t('leave.site')}</span>\n      </Modal>\n    </>\n  );\n};\nexport default Sidebar;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,OAAO,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AACnE,SAASC,IAAI,EAAEC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AACjE,SACEC,cAAc,EACdC,gBAAgB,EAChBC,kBAAkB,EAClBC,cAAc,QACT,mBAAmB;AAC1B,SAASC,OAAO,EAAEC,IAAI,EAAEC,KAAK,EAAEC,MAAM,EAAEC,KAAK,EAAEC,KAAK,QAAQ,MAAM;AACjE,SAASC,YAAY,EAAEC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACpE,SAASC,OAAO,EAAEC,SAAS,EAAEC,OAAO,QAAQ,sBAAsB;AAClE,SAASC,cAAc,QAAQ,eAAe;AAC9C,OAAOC,SAAS,MAAM,cAAc;AACpC,OAAOC,cAAc,MAAM,2BAA2B;AACtD,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,SAASC,kBAAkB,QAAQ,uBAAuB;AAC1D,OAAOC,iBAAiB,MAAM,sBAAsB;AACpD,OAAOC,IAAI,MAAM,oBAAoB;AACrC,SAASC,gBAAgB,QAAQ,gBAAgB;AACjD,OAAOC,UAAU,MAAM,yBAAyB;AAChD,OAAOC,OAAO,MAAM,uBAAuB;AAC3C,OAAOC,UAAU,MAAM,eAAe;AACtC,SAASC,KAAK,QAAQ,aAAa;AACnC,SAASC,SAAS,QAAQ,sBAAsB;AAChD,SAASC,cAAc,QAAQ,sBAAsB;AACrD,SAASC,IAAI,IAAIC,SAAS,QAAQ,qBAAqB;AACvD,OAAOC,YAAY,MAAM,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAChD,MAAM;EAAEC;AAAM,CAAC,GAAG9B,MAAM;AAExB,MAAM+B,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,eAAA;EACpB,MAAM;IAAEC;EAAE,CAAC,GAAGzB,cAAc,CAAC,CAAC;EAC9B,MAAM0B,QAAQ,GAAG3C,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAE4C;EAAS,CAAC,GAAG7C,WAAW,CAAC,CAAC;EAClC,MAAM;IAAE8C;EAAK,CAAC,GAAGhC,WAAW,CAAEiC,KAAK,IAAKA,KAAK,CAACC,IAAI,EAAEpC,YAAY,CAAC;EACjE,MAAM,CAACqC,cAAc,EAAEC,iBAAiB,CAAC,GAAGtD,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAMuD,YAAY,GAAGtD,MAAM,CAAC,IAAI,CAAC;EACjC,MAAM;IAAEuD,aAAa;IAAEC,YAAY;IAAEC;EAAgB,CAAC,GAAGxC,WAAW,CACjEiC,KAAK,IAAKA,KAAK,CAACQ,cAAc,CAACC,QAAQ,EACxC5C,YACF,CAAC;EACD,MAAM;IAAE6C;EAAa,CAAC,GAAG3C,WAAW,CACjCiC,KAAK,IAAKA,KAAK,CAACW,KAAK,CAACA,KAAK,EAC5B9C,YACF,CAAC;EACD,MAAM;IAAE+C;EAAU,CAAC,GAAG7C,WAAW,CAAEiC,KAAK,IAAKA,KAAK,CAACa,QAAQ,EAAEhD,YAAY,CAAC;EAC1E,MAAMiD,QAAQ,GAAGhD,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACiD,SAAS,EAAEC,YAAY,CAAC,GAAGnE,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM;IAAEoE;EAAO,CAAC,GAAGlD,WAAW,CAAEiC,KAAK,IAAKA,KAAK,CAACiB,MAAM,EAAEpD,YAAY,CAAC;EACrE,MAAM;IAAE8C;EAAM,CAAC,GAAG5C,WAAW,CAAEiC,KAAK,IAAKA,KAAK,CAACW,KAAK,EAAE9C,YAAY,CAAC;EACnE,MAAMqD,UAAU,GAAGtE,OAAO,CACxB,MAAM,CAAC,CAAC+D,KAAK,CAACO,UAAU,IAAI,CAAAnB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoB,IAAI,MAAK,OAAO,EAClD,CAACR,KAAK,EAAEZ,IAAI,CACd,CAAC;EACD,MAAMqB,MAAM,GAAGxE,OAAO,CAAC,MAAM;IAAA,IAAAyE,UAAA;IAC3B,MAAMC,qBAAqB,GAAGf,eAAe,KAAK,GAAG;IACrD,MAAMgB,aAAa,GAAG;MAAEH,MAAM,EAAE,EAAE;MAAEI,SAAS,EAAE;IAAG,CAAC;IACnD,IAAI,CAACF,qBAAqB,EAAE;MAC1BC,aAAa,CAACC,SAAS,CAACC,IAAI,CAC1B,eAAe,EACf,kBAAkB,EAClB,oBACF,CAAC;IACH;IACA,OAAOC,gBAAgB,EAAAL,UAAA,GACrBtB,IAAI,CAAC4B,IAAI,cAAAN,UAAA,uBAATA,UAAA,CACIO,MAAM,CAAEC,IAAI,IAAK,CAACN,aAAa,CAACH,MAAM,CAACU,QAAQ,CAACD,IAAI,CAACE,IAAI,CAAC,CAAC,CAC5DC,GAAG,CAAEH,IAAI;MAAA,IAAAI,aAAA;MAAA,OAAM;QACd,GAAGJ,IAAI;QACPK,OAAO,GAAAD,aAAA,GAAEJ,IAAI,CAACK,OAAO,cAAAD,aAAA,uBAAZA,aAAA,CAAcL,MAAM,CAC1BO,GAAG,IAAK,CAACZ,aAAa,CAACC,SAAS,CAACM,QAAQ,CAACK,GAAG,CAACJ,IAAI,CACrD;MACF,CAAC;IAAA,CAAC,CACN,CAAC;EACH,CAAC,EAAE,CAAChC,IAAI,CAAC,CAAC;EACV,MAAMqC,MAAM,GAAGhB,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEiB,IAAI,CAAER,IAAI,IAAK/B,QAAQ,CAACgC,QAAQ,CAACD,IAAI,CAACS,GAAG,CAAC,CAAC;EAClE,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAG3F,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACoC,IAAI,EAAEwD,OAAO,CAAC,GAAG5F,QAAQ,CAACqE,UAAU,GAAGhC,SAAS,CAACwD,MAAM,GAAGtB,MAAM,CAAC;EAExEjC,YAAY,CAAC,MAAM;IACjB,IAAI+B,UAAU,EAAE;MACduB,OAAO,CAACvD,SAAS,CAACwD,MAAM,CAAC;IAC3B,CAAC,MAAM;MACLD,OAAO,CAACrB,MAAM,CAAC;IACjB;EACF,CAAC,EAAE,CAACT,KAAK,EAAEZ,IAAI,CAAC,CAAC;EAEjB,MAAM4C,UAAU,GAAId,IAAI,IAAK;IAC3B,IAAI,OAAOA,IAAI,CAACS,GAAG,KAAK,WAAW,EAAE;IACrC,IAAIT,IAAI,CAACE,IAAI,KAAK,QAAQ,EAAE;MAC1B5B,iBAAiB,CAAC,IAAI,CAAC;MACvB;IACF;IACA,MAAMlB,IAAI,GAAG;MACX,GAAG4C,IAAI;MACPe,IAAI,EAAEC,SAAS;MACfC,QAAQ,EAAED,SAAS;MACnBE,OAAO,EAAE;IACX,CAAC;IACDjC,QAAQ,CAAC5C,OAAO,CAACe,IAAI,CAAC,CAAC;IACvBY,QAAQ,CAAE,IAAGgC,IAAI,CAACS,GAAI,EAAC,CAAC;EAC1B,CAAC;EAED,SAASZ,gBAAgBA,CAACN,MAAM,EAAE;IAChC,IAAI4B,IAAI,GAAG5B,MAAM;IACjB,IAAIH,MAAM,CAACgC,IAAI,KAAK,MAAM,EAAE;MAC1BD,IAAI,GAAG5B,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEQ,MAAM,CAAEC,IAAI,IAAK,CAAAA,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEE,IAAI,MAAK,QAAQ,CAAC;IAC1D;IACA,IAAIzB,YAAY,KAAK,OAAO,EAAE;MAC5B0C,IAAI,GAAG5B,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEQ,MAAM,CAAEC,IAAI,IAAK,CAAAA,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEE,IAAI,MAAK,UAAU,CAAC;IAC5D;IACA,IAAI1B,aAAa,KAAK,GAAG,EAAE;MACzB2C,IAAI,GAAG5B,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEQ,MAAM,CAAEC,IAAI,IAAK,CAAAA,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEE,IAAI,MAAK,SAAS,CAAC;IAC3D;IACA,OAAOiB,IAAI;EACb;EAEA,MAAME,WAAW,GAAIC,KAAK,IAAK;IAC7BA,KAAK,CAACC,eAAe,CAAC,CAAC;IACvBtC,QAAQ,CAACvC,kBAAkB,CAAC,CAAC,CAAC;EAChC,CAAC;EAED,MAAM8E,WAAW,GAAIC,OAAO,IAAK;IAC/B,MAAMrE,IAAI,GAAG;MAAE,GAAGqE,OAAO;MAAEV,IAAI,EAAEC;IAAU,CAAC;IAC5C/B,QAAQ,CAAC9C,OAAO,CAACiB,IAAI,CAAC,CAAC;EACzB,CAAC;EAED,MAAMsE,QAAQ,GAAGA,CAAA,KAAM;IACrBzE,KAAK,CAAC,MAAM;MACVgC,QAAQ,CAAC/B,SAAS,CAAC,CAAC,CAAC;MACrB+B,QAAQ,CAAC7C,SAAS,CAAC,CAAC,CAAC;MACrB6C,QAAQ,CAAC9B,cAAc,CAAC,IAAI,CAAC,CAAC;IAChC,CAAC,CAAC;IACFmB,iBAAiB,CAAC,KAAK,CAAC;IACxBqD,YAAY,CAACC,UAAU,CAAC,OAAO,CAAC;IAChC5D,QAAQ,CAAC,QAAQ,CAAC;EACpB,CAAC;EAED,MAAM6D,YAAY,GAAGA,CAAA,KAAMvD,iBAAiB,CAAC,KAAK,CAAC;EAEnD,SAASwD,aAAaA,CAACvC,MAAM,EAAE;IAC7B,MAAMwC,UAAU,GAAG,EAAE;IACrBxC,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEY,GAAG,CAAEH,IAAI,IAAK;MAAA,IAAAgC,cAAA;MACpBD,UAAU,CAACnC,IAAI,CAACI,IAAI,CAAC;MACrBA,IAAI,aAAJA,IAAI,wBAAAgC,cAAA,GAAJhC,IAAI,CAAEK,OAAO,cAAA2B,cAAA,uBAAbA,cAAA,CAAe7B,GAAG,CAAEG,GAAG,IAAK;QAAA,IAAA2B,aAAA;QAC1BF,UAAU,CAACnC,IAAI,CAACU,GAAG,CAAC;QACpBA,GAAG,aAAHA,GAAG,wBAAA2B,aAAA,GAAH3B,GAAG,CAAEW,QAAQ,cAAAgB,aAAA,uBAAbA,aAAA,CAAe9B,GAAG,CAAE+B,KAAK,IAAK;UAC5BH,UAAU,CAACnC,IAAI,CAACsC,KAAK,CAAC;QACxB,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,OAAOH,UAAU;EACnB;EAEA,MAAMI,UAAU,GAAGL,aAAa,CAAC1E,IAAI,CAAC;EAEtC,MAAMgF,QAAQ,GACZ1B,UAAU,CAAC2B,MAAM,GAAG,CAAC,GACjBF,UAAU,CAACpC,MAAM,CAAEuC,KAAK;IAAA,IAAAC,WAAA;IAAA,OACtBxE,CAAC,EAAAwE,WAAA,GAACD,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEpC,IAAI,cAAAqC,WAAA,cAAAA,WAAA,GAAI,EAAE,CAAC,CACjBC,WAAW,CAAC,CAAC,CACbvC,QAAQ,CAACS,UAAU,CAAC8B,WAAW,CAAC,CAAC,CAAC;EAAA,CACvC,CAAC,GACDpF,IAAI;EAEV,oBACEI,OAAA,CAAAE,SAAA;IAAAuD,QAAA,gBACEzD,OAAA,CAACG,KAAK;MACJ8E,SAAS,EAAC,qBAAqB;MAC/BC,KAAK,EAAE,GAAI;MACXC,SAAS,EAAE9D,YAAa;MACxB+D,KAAK,EAAE;QAAEC,MAAM,EAAE,OAAO;QAAEC,GAAG,EAAE;MAAE,CAAE;MAAA7B,QAAA,gBAEnCzD,OAAA,CAACR,UAAU;QAACkB,IAAI,EAAEA;MAAK;QAAA6E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC1B1F,OAAA;QAAKiF,SAAS,EAAC,eAAe;QAACU,OAAO,EAAE9B,WAAY;QAAAJ,QAAA,eAClDzD,OAAA,CAACjC,gBAAgB;UAAAwH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CAAC,EACLrE,YAAY,iBACXrB,OAAA;QAAKiF,SAAS,EAAC,6BAA6B;QAAAxB,QAAA,eAC1CzD,OAAA,CAACb,iBAAiB;UAAAoG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClB,CACN,EAEA,CAACrE,YAAY,gBACZrB,OAAA,CAAC5B,KAAK;QAAC6G,SAAS,EAAC,0CAA0C;QAAAxB,QAAA,gBACzDzD,OAAA;UAAMiF,SAAS,EAAC,aAAa;UAACU,OAAO,EAAEA,CAAA,KAAMhE,YAAY,CAAC,IAAI,CAAE;UAAA8B,QAAA,gBAC9DzD,OAAA;YACEiF,SAAS,EAAC,gBAAgB;YAC1BW,GAAG,GAAAtF,eAAA,GACDiB,SAAS,CAACyB,IAAI,CAAER,IAAI,IAAKA,IAAI,CAACqD,MAAM,KAAKzG,IAAI,CAAC0G,QAAQ,CAAC,cAAAxF,eAAA,uBAAvDA,eAAA,CAAyDyF,GAC1D;YACDC,GAAG,EAAEtF,IAAI,CAACuF;UAAS;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpB,CAAC,eACF1F,OAAA;YAAMiF,SAAS,EAAC,cAAc;YAAAxB,QAAA,EAAErE,IAAI,CAAC0G;UAAQ;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACrD1F,OAAA,CAACX,gBAAgB;YAAC6G,IAAI,EAAE;UAAG;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC,eACP1F,OAAA;UAAMiF,SAAS,EAAC,QAAQ;UAAAxB,QAAA,gBACtBzD,OAAA,CAACb,iBAAiB;YAAAoG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACrB1F,OAAA,CAACf,eAAe;YAAAsG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,gBAER1F,OAAA;QAAKiF,SAAS,EAAC,aAAa;QAACU,OAAO,EAAE9B,WAAY;QAAAJ,QAAA,eAChDzD,OAAA,CAAChC,kBAAkB;UAAAuH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB,CACN,eACD1F,OAAA,CAAC9B,OAAO;QAACkH,KAAK,EAAE;UAAEe,MAAM,EAAE;QAAS;MAAE;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EAEvC,CAACrE,YAAY,iBACZrB,OAAA;QAAMiF,SAAS,EAAC,yCAAyC;QAAAxB,QAAA,eACvDzD,OAAA,CAACzB,KAAK;UACJ6H,WAAW,EAAC,QAAQ;UACpBhB,KAAK,EAAE;YAAEF,KAAK,EAAE;UAAM,CAAE;UACxBmB,KAAK,EAAEnD,UAAW;UAClBoD,QAAQ,EAAGxC,KAAK,IAAK;YACnBX,aAAa,CAACW,KAAK,CAACyC,MAAM,CAACF,KAAK,CAAC;UACnC,CAAE;UACFG,MAAM,eAAExG,OAAA,CAAC/B,cAAc;YAAAsH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACP,eAED1F,OAAA,CAACV,UAAU;QACTmH,UAAU;QACVC,aAAa,EAAEC,MAAM,CAACC,WAAW,GAAG,GAAG,GAAG,MAAM,GAAG,MAAO;QAC1DC,aAAa,EAAEF,MAAM,CAACC,WAAW,GAAG,GAAG,GAAG,MAAM,GAAG,MAAO;QAC1DE,QAAQ;QAAArD,QAAA,eAERzD,OAAA,CAAC7B,IAAI;UACHmD,KAAK,EAAC,OAAO;UACbyF,IAAI,EAAC,QAAQ;UACbC,mBAAmB,EAAE,CAACC,MAAM,CAAClE,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEmE,EAAE,CAAC,CAAE;UAC1CC,eAAe,EACb,CAAC9F,YAAY,GAAGzB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE+C,GAAG,CAAC,CAACyE,CAAC,EAAEC,GAAG,KAAKD,CAAC,CAACF,EAAE,GAAG,GAAG,GAAGG,GAAG,CAAC,GAAG,EAC3D;UAAA5D,QAAA,EAEAmB,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEjC,GAAG,CAAC,CAACH,IAAI,EAAE6E,GAAG;YAAA,IAAAC,cAAA;YAAA,OACvB,EAAAA,cAAA,GAAA9E,IAAI,CAACK,OAAO,cAAAyE,cAAA,uBAAZA,cAAA,CAAczC,MAAM,IAAG,CAAC,gBACtB7E,OAAA,CAACT,OAAO;cAENgI,KAAK,EAAEhH,CAAC,CAACiC,IAAI,CAACE,IAAI,CAAE;cACpBa,IAAI,EAAEvE,cAAc,CAACwD,IAAI,CAACe,IAAI,CAAE;cAAAE,QAAA,EAE/BjB,IAAI,CAACK,OAAO,CAACF,GAAG,CAAC,CAACE,OAAO,EAAE2E,GAAG;gBAAA,IAAAC,iBAAA,EAAAC,kBAAA;gBAAA,OAC7B,EAAAD,iBAAA,GAAA5E,OAAO,CAACY,QAAQ,cAAAgE,iBAAA,uBAAhBA,iBAAA,CAAkB5C,MAAM,IAAG,CAAC,gBAC1B7E,OAAA,CAACT,OAAO;kBACNoI,WAAW,EAAE,IAAK;kBAElBJ,KAAK,EAAEhH,CAAC,CAACsC,OAAO,CAACH,IAAI,CAAE;kBACvBa,IAAI,EAAEvE,cAAc,CAAC6D,OAAO,CAACU,IAAI,CAAE;kBACnCqE,YAAY,EAAEA,CAAA,KAAMtE,UAAU,CAACT,OAAO,CAAE;kBAAAY,QAAA,GAAAiE,kBAAA,GAEvC7E,OAAO,CAACY,QAAQ,cAAAiE,kBAAA,uBAAhBA,kBAAA,CAAkB/E,GAAG,CAAC,CAACG,GAAG,EAAE+E,GAAG,kBAC9B7H,OAAA,CAAC7B,IAAI,CAAC2J,IAAI;oBAERvE,IAAI,EAAEvE,cAAc,CAAC8D,GAAG,CAACS,IAAI,CAAE;oBAAAE,QAAA,eAE/BzD,OAAA,CAACrC,IAAI;sBACHoK,EAAE,EAAE,GAAG,GAAGjF,GAAG,CAACG,GAAI;sBAClB0C,OAAO,EAAEA,CAAA,KAAM3B,WAAW,CAAClB,GAAG,CAAE;sBAAAW,QAAA,eAEhCzD,OAAA;wBAAAyD,QAAA,EAAOlD,CAAC,CAACuC,GAAG,CAACJ,IAAI;sBAAC;wBAAA6C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtB;kBAAC,GARF,OAAO,GAAGmC,GAAG,GAAG/E,GAAG,CAACoE,EAAE;oBAAA3B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OASlB,CACZ;gBAAC,GAjBG7C,OAAO,CAACqE,EAAE,GAAG,GAAG,GAAGM,GAAG;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAkBpB,CAAC,gBAEV1F,OAAA,CAAC7B,IAAI,CAAC2J,IAAI;kBAERvE,IAAI,EAAEvE,cAAc,CAAC6D,OAAO,CAACU,IAAI,CAAE;kBAAAE,QAAA,eAEnCzD,OAAA,CAACrC,IAAI;oBACHoK,EAAE,EAAE,GAAG,GAAGlF,OAAO,CAACI,GAAI;oBACtB0C,OAAO,EAAEA,CAAA,KAAMrC,UAAU,CAACT,OAAO,CAAE;oBAAAY,QAAA,eAEnCzD,OAAA;sBAAAyD,QAAA,EAAOlD,CAAC,CAACsC,OAAO,CAACH,IAAI;oBAAC;sBAAA6C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1B;gBAAC,GARF7C,OAAO,CAACqE,EAAE;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OASN,CACZ;cAAA,CACH;YAAC,GAxCIlD,IAAI,CAAC0E,EAAE,GAAG,GAAG,GAAGG,GAAG;cAAA9B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAyCjB,CAAC,gBAEV1F,OAAA,CAAC7B,IAAI,CAAC2J,IAAI;cAAevE,IAAI,EAAEvE,cAAc,CAACwD,IAAI,CAACe,IAAI,CAAE;cAAAE,QAAA,eACvDzD,OAAA,CAACrC,IAAI;gBAACoK,EAAE,EAAE,GAAG,GAAGvF,IAAI,CAACS,GAAI;gBAAC0C,OAAO,EAAEA,CAAA,KAAMrC,UAAU,CAACd,IAAI,CAAE;gBAAAiB,QAAA,eACxDzD,OAAA;kBAAAyD,QAAA,EAAOlD,CAAC,CAACiC,IAAI,CAACE,IAAI;gBAAC;kBAAA6C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB;YAAC,GAHOlD,IAAI,CAAC0E,EAAE;cAAA3B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAIZ,CACZ;UAAA,CACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC,EAEPhE,SAAS,iBACR1B,OAAA,CAACjB,SAAS;MACRiJ,OAAO,EAAEtG,SAAU;MACnB2C,YAAY,EAAEA,CAAA,KAAM1C,YAAY,CAAC,KAAK;IAAE;MAAA4D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzC,CACF,eAED1F,OAAA,CAAC1B,KAAK;MACJ0J,OAAO,EAAEnH,cAAe;MACxBoH,IAAI,EAAE/D,QAAS;MACfgE,QAAQ,EAAE7D,YAAa;MACvB8D,QAAQ;MAAA1E,QAAA,gBAERzD,OAAA,CAAClC,cAAc;QACbsH,KAAK,EAAE;UAAEgD,QAAQ,EAAE,MAAM;UAAEC,KAAK,EAAE;QAAO,CAAE;QAC3C/G,KAAK,EAAC;MAAS;QAAAiE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChB,CAAC,eACF1F,OAAA;QAAMiF,SAAS,EAAC,MAAM;QAAAxB,QAAA,EAAElD,CAAC,CAAC,YAAY;MAAC;QAAAgF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1C,CAAC;EAAA,eACR,CAAC;AAEP,CAAC;AAACrF,EAAA,CA5RID,OAAO;EAAA,QACGtB,cAAc,EACXjB,WAAW,EACPD,WAAW,EACfc,WAAW,EAG6BA,WAAW,EAI3CA,WAAW,EAIdA,WAAW,EAChBD,WAAW,EAETC,WAAW,EACZA,WAAW,EA8B7BoB,YAAY;AAAA;AAAAwI,EAAA,GAjDRlI,OAAO;AA6Rb,eAAeA,OAAO;AAAC,IAAAkI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}