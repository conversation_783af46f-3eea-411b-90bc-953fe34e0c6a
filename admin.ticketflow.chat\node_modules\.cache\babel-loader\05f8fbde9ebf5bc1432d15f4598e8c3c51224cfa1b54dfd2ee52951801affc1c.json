{"ast": null, "code": "var _jsxFileName = \"C:\\\\OSPanel\\\\home\\\\admin.ticketflow.chat\\\\src\\\\views\\\\shop-tag\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useContext, useEffect, useState } from 'react';\nimport { Button, Card, Image, Space, Table, Tabs, Tag } from 'antd';\nimport { IMG_URL } from '../../configs/app-global';\nimport { useNavigate } from 'react-router-dom';\nimport { CopyOutlined, DeleteOutlined, EditOutlined, PlusCircleOutlined } from '@ant-design/icons';\nimport CustomModal from '../../components/modal';\nimport { Context } from '../../context/context';\nimport { shallowEqual, useDispatch, useSelector } from 'react-redux';\nimport { addMenu, disableRefetch, setMenuData } from '../../redux/slices/menu';\nimport shopTagService from '../../services/shopTag';\nimport { fetchShopTag } from '../../redux/slices/shopTag';\nimport { toast } from 'react-toastify';\nimport { useTranslation } from 'react-i18next';\nimport DeleteButton from '../../components/delete-button';\nimport FilterColumns from '../../components/filter-column';\nimport ResultModal from '../../components/result-modal';\nimport { FaTrashRestoreAlt } from 'react-icons/fa';\nimport useDidUpdate from '../../helpers/useDidUpdate';\nimport moment from 'moment';\nimport formatSortType from '../../helpers/formatSortType';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst roles = ['published', 'deleted_at'];\nconst {\n  TabPane\n} = Tabs;\nconst colors = ['blue', 'red', 'gold', 'volcano', 'cyan', 'lime'];\nconst ShopTag = () => {\n  _s();\n  var _activeMenu$data, _activeMenu$data2, _activeMenu$data3;\n  const {\n    t\n  } = useTranslation();\n  const dispatch = useDispatch();\n  const navigate = useNavigate();\n  const [restore, setRestore] = useState(null);\n  const {\n    setIsModalVisible\n  } = useContext(Context);\n  const [loadingBtn, setLoadingBtn] = useState(false);\n  const [role, setRole] = useState('published');\n  const {\n    activeMenu\n  } = useSelector(state => state.menu, shallowEqual);\n  const {\n    shopTag,\n    meta,\n    loading,\n    params\n  } = useSelector(state => state.shopTag, shallowEqual);\n  const [id, setId] = useState(null);\n  const data = activeMenu.data;\n  const immutable = (data === null || data === void 0 ? void 0 : data.role) || role;\n  const paramsData = {\n    sort: data === null || data === void 0 ? void 0 : data.sort,\n    column: data === null || data === void 0 ? void 0 : data.column,\n    perPage: data === null || data === void 0 ? void 0 : data.perPage,\n    page: data === null || data === void 0 ? void 0 : data.page,\n    status: immutable === 'deleted_at' ? null : immutable,\n    deleted_at: immutable === 'deleted_at' ? immutable : null\n  };\n  const [columns, setColumns] = useState([{\n    title: t('id'),\n    dataIndex: 'id',\n    key: 'id',\n    sorter: true,\n    is_show: true\n  }, {\n    title: t('title'),\n    dataIndex: 'title',\n    key: 'title',\n    is_show: true,\n    render: (_, row) => {\n      var _row$translation;\n      return (_row$translation = row.translation) === null || _row$translation === void 0 ? void 0 : _row$translation.title;\n    }\n  }, {\n    title: t('translations'),\n    dataIndex: 'locales',\n    is_show: true,\n    render: (_, row) => {\n      var _row$locales;\n      return /*#__PURE__*/_jsxDEV(Space, {\n        children: (_row$locales = row.locales) === null || _row$locales === void 0 ? void 0 : _row$locales.map((item, index) => /*#__PURE__*/_jsxDEV(Tag, {\n          color: [colors[index]],\n          className: \"text-uppercase\",\n          children: item\n        }, `${row.id}-locale-${index}`, false, {\n          fileName: _jsxFileName,\n          lineNumber: 77,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 75,\n        columnNumber: 9\n      }, this);\n    }\n  }, {\n    title: t('image'),\n    dataIndex: 'img',\n    key: 'img',\n    is_show: true,\n    render: (img, row) => {\n      return /*#__PURE__*/_jsxDEV(Image, {\n        src: !row.deleted_at ? IMG_URL + img : 'https://fakeimg.pl/640x360',\n        alt: \"img_gallery\",\n        width: 100,\n        className: \"rounded\",\n        preview: true,\n        placeholder: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 95,\n        columnNumber: 11\n      }, this);\n    }\n  }, {\n    title: t('created.at'),\n    dataIndex: 'created_at',\n    key: 'created_at',\n    is_show: true,\n    render: created_at => moment(created_at).format('DD/MM/YYYY')\n  }, {\n    title: t('options'),\n    key: 'options',\n    dataIndex: 'options',\n    is_show: true,\n    render: (_, row) => /*#__PURE__*/_jsxDEV(Space, {\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        type: \"primary\",\n        icon: /*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 122,\n          columnNumber: 19\n        }, this),\n        onClick: () => goToEdit(row),\n        disabled: row.deleted_at\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 120,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        icon: /*#__PURE__*/_jsxDEV(CopyOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 127,\n          columnNumber: 19\n        }, this),\n        onClick: () => goToClone(row),\n        disabled: row.deleted_at\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 126,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(DeleteButton, {\n        disabled: row.deleted_at,\n        icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 133,\n          columnNumber: 19\n        }, this),\n        onClick: () => {\n          setIsModalVisible(true);\n          setId([row.id]);\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 131,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 119,\n      columnNumber: 9\n    }, this)\n  }]);\n  const goToAddBanners = () => {\n    dispatch(addMenu({\n      id: 'shop-tag/add',\n      url: 'shop-tag/add',\n      name: t('add.shop.tag')\n    }));\n    navigate('/shop-tag/add');\n  };\n  const goToEdit = row => {\n    dispatch(addMenu({\n      url: `shop-tag/${row.id}`,\n      id: 'shop_tag_edit',\n      name: t('edit.shop.tag')\n    }));\n    navigate(`/shop-tag/${row.id}`);\n  };\n  const goToClone = row => {\n    dispatch(addMenu({\n      url: `shop-tag/clone/${row.id}`,\n      id: 'shop_tag_clone',\n      name: t('clone.shop.tag')\n    }));\n    navigate(`/shop-tag/clone/${row.id}`);\n  };\n  const tagDelete = () => {\n    setLoadingBtn(true);\n    const params = {\n      ...Object.assign({}, ...id.map((item, index) => ({\n        [`ids[${index}]`]: item\n      })))\n    };\n    shopTagService.delete(params).then(() => {\n      dispatch(fetchShopTag());\n      toast.success(t('successfully.deleted'));\n      setIsModalVisible(false);\n    }).finally(() => {\n      setLoadingBtn(false);\n    });\n  };\n  const tagRestoreAll = () => {\n    setLoadingBtn(true);\n    shopTagService.restoreAll().then(() => {\n      dispatch(fetchShopTag(paramsData));\n      toast.success(t('successfully.deleted'));\n    }).finally(() => {\n      setRestore(null);\n      setLoadingBtn(false);\n    });\n  };\n  const tagDropAll = () => {\n    setLoadingBtn(true);\n    shopTagService.dropAll().then(() => {\n      dispatch(fetchShopTag());\n      toast.success(t('successfully.deleted'));\n    }).finally(() => {\n      setRestore(null);\n      setLoadingBtn(false);\n    });\n  };\n  useDidUpdate(() => {\n    dispatch(fetchShopTag(paramsData));\n  }, [activeMenu.data]);\n  useEffect(() => {\n    if (activeMenu.refetch) {\n      dispatch(fetchShopTag(paramsData));\n      dispatch(disableRefetch(activeMenu));\n    }\n  }, [activeMenu.refetch]);\n  function onChangePagination(pagination, filter, sorter) {\n    const {\n      pageSize: perPage,\n      current: page\n    } = pagination;\n    const {\n      field: column,\n      order\n    } = sorter;\n    const sort = formatSortType(order);\n    dispatch(setMenuData({\n      activeMenu,\n      data: {\n        ...activeMenu.data,\n        perPage,\n        page,\n        column,\n        sort\n      }\n    }));\n  }\n  const rowSelection = {\n    selectedRowKeys: id,\n    onChange: key => {\n      setId(key);\n    }\n  };\n  const allDelete = () => {\n    if (id === null || id.length === 0) {\n      toast.warning(t('select.the.product'));\n    } else {\n      setIsModalVisible(true);\n    }\n  };\n  const handleFilter = items => {\n    const data = activeMenu.data;\n    dispatch(setMenuData({\n      activeMenu,\n      data: {\n        ...data,\n        ...items\n      }\n    }));\n  };\n  return /*#__PURE__*/_jsxDEV(Card, {\n    title: t('shop.tags'),\n    extra: /*#__PURE__*/_jsxDEV(Space, {\n      wrap: true,\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        type: \"primary\",\n        icon: /*#__PURE__*/_jsxDEV(PlusCircleOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 282,\n          columnNumber: 19\n        }, this),\n        onClick: goToAddBanners,\n        children: t('add.tag')\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 280,\n        columnNumber: 11\n      }, this), immutable === 'published' ? /*#__PURE__*/_jsxDEV(DeleteButton, {\n        size: \"\",\n        onClick: () => setRestore({\n          delete: true\n        }),\n        children: t('delete.all')\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 289,\n        columnNumber: 13\n      }, this) : /*#__PURE__*/_jsxDEV(DeleteButton, {\n        size: \"\",\n        icon: /*#__PURE__*/_jsxDEV(FaTrashRestoreAlt, {\n          className: \"mr-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 295,\n          columnNumber: 21\n        }, this),\n        onClick: () => setRestore({\n          restore: true\n        }),\n        children: t('restore.all')\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 293,\n        columnNumber: 13\n      }, this), immutable !== 'deleted_at' && /*#__PURE__*/_jsxDEV(DeleteButton, {\n        size: \"\",\n        onClick: allDelete,\n        children: t('delete.selected')\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 303,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(FilterColumns, {\n        setColumns: setColumns,\n        columns: columns\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 308,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 279,\n      columnNumber: 9\n    }, this),\n    children: [/*#__PURE__*/_jsxDEV(Tabs, {\n      className: \"mt-3\",\n      activeKey: immutable,\n      onChange: key => {\n        handleFilter({\n          role: key,\n          page: 1\n        });\n        setRole(key);\n      },\n      type: \"card\",\n      children: roles.map(item => /*#__PURE__*/_jsxDEV(TabPane, {\n        tab: t(item)\n      }, item, false, {\n        fileName: _jsxFileName,\n        lineNumber: 322,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 312,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Table, {\n      scroll: {\n        x: true\n      },\n      rowSelection: rowSelection,\n      columns: columns === null || columns === void 0 ? void 0 : columns.filter(item => item.is_show),\n      dataSource: shopTag,\n      pagination: {\n        pageSize: params.perPage,\n        page: ((_activeMenu$data = activeMenu.data) === null || _activeMenu$data === void 0 ? void 0 : _activeMenu$data.page) || 1,\n        total: meta.total,\n        defaultCurrent: (_activeMenu$data2 = activeMenu.data) === null || _activeMenu$data2 === void 0 ? void 0 : _activeMenu$data2.page,\n        current: (_activeMenu$data3 = activeMenu.data) === null || _activeMenu$data3 === void 0 ? void 0 : _activeMenu$data3.page\n      },\n      rowKey: record => record.id,\n      loading: loading,\n      onChange: onChangePagination\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 325,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(CustomModal, {\n      click: tagDelete,\n      text: t('delete'),\n      loading: loadingBtn,\n      setText: setId\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 341,\n      columnNumber: 7\n    }, this), restore && /*#__PURE__*/_jsxDEV(ResultModal, {\n      open: restore,\n      handleCancel: () => setRestore(null),\n      click: restore.restore ? tagRestoreAll : tagDropAll,\n      text: restore.restore ? t('restore.modal.text') : t('read.carefully'),\n      subTitle: restore.restore ? '' : t('confirm.deletion'),\n      loading: loadingBtn,\n      setText: setId\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 348,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 276,\n    columnNumber: 5\n  }, this);\n};\n_s(ShopTag, \"gUoVlufWudKnD0ZFOFEfZxp72Ug=\", false, function () {\n  return [useTranslation, useDispatch, useNavigate, useSelector, useSelector, useDidUpdate];\n});\n_c = ShopTag;\nexport default ShopTag;\nvar _c;\n$RefreshReg$(_c, \"ShopTag\");", "map": {"version": 3, "names": ["React", "useContext", "useEffect", "useState", "<PERSON><PERSON>", "Card", "Image", "Space", "Table", "Tabs", "Tag", "IMG_URL", "useNavigate", "CopyOutlined", "DeleteOutlined", "EditOutlined", "PlusCircleOutlined", "CustomModal", "Context", "shallowEqual", "useDispatch", "useSelector", "addMenu", "disable<PERSON><PERSON><PERSON><PERSON>", "setMenuData", "shopTagService", "fetchShopTag", "toast", "useTranslation", "DeleteButton", "FilterColumns", "ResultModal", "FaTrashRestoreAlt", "useDidUpdate", "moment", "formatSortType", "jsxDEV", "_jsxDEV", "roles", "TabPane", "colors", "ShopTag", "_s", "_activeMenu$data", "_activeMenu$data2", "_activeMenu$data3", "t", "dispatch", "navigate", "restore", "setRestore", "setIsModalVisible", "loadingBtn", "setLoadingBtn", "role", "setRole", "activeMenu", "state", "menu", "shopTag", "meta", "loading", "params", "id", "setId", "data", "immutable", "paramsData", "sort", "column", "perPage", "page", "status", "deleted_at", "columns", "setColumns", "title", "dataIndex", "key", "sorter", "is_show", "render", "_", "row", "_row$translation", "translation", "_row$locales", "children", "locales", "map", "item", "index", "color", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "img", "src", "alt", "width", "preview", "placeholder", "created_at", "format", "type", "icon", "onClick", "goToEdit", "disabled", "goToClone", "goToAddBanners", "url", "name", "tagDelete", "Object", "assign", "delete", "then", "success", "finally", "tagRestoreAll", "restoreAll", "tagDropAll", "dropAll", "refetch", "onChangePagination", "pagination", "filter", "pageSize", "current", "field", "order", "rowSelection", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onChange", "allDelete", "length", "warning", "handleFilter", "items", "extra", "wrap", "size", "active<PERSON><PERSON>", "tab", "scroll", "x", "dataSource", "total", "defaultCurrent", "<PERSON><PERSON><PERSON>", "record", "click", "text", "setText", "open", "handleCancel", "subTitle", "_c", "$RefreshReg$"], "sources": ["C:/OSPanel/home/<USER>/src/views/shop-tag/index.js"], "sourcesContent": ["import React, { useContext, useEffect, useState } from 'react';\nimport { <PERSON>ton, Card, Image, Space, Table, Tabs, Tag } from 'antd';\nimport { IMG_URL } from '../../configs/app-global';\nimport { useNavigate } from 'react-router-dom';\nimport {\n  CopyOutlined,\n  DeleteOutlined,\n  EditOutlined,\n  PlusCircleOutlined,\n} from '@ant-design/icons';\nimport CustomModal from '../../components/modal';\nimport { Context } from '../../context/context';\nimport { shallowEqual, useDispatch, useSelector } from 'react-redux';\nimport { addMenu, disableRefetch, setMenuData } from '../../redux/slices/menu';\nimport shopTagService from '../../services/shopTag';\nimport { fetchShopTag } from '../../redux/slices/shopTag';\nimport { toast } from 'react-toastify';\nimport { useTranslation } from 'react-i18next';\nimport DeleteButton from '../../components/delete-button';\nimport FilterColumns from '../../components/filter-column';\nimport ResultModal from '../../components/result-modal';\nimport { FaTrashRestoreAlt } from 'react-icons/fa';\nimport useDidUpdate from '../../helpers/useDidUpdate';\nimport moment from 'moment';\nimport formatSortType from '../../helpers/formatSortType';\n\nconst roles = ['published', 'deleted_at'];\nconst { TabPane } = Tabs;\nconst colors = ['blue', 'red', 'gold', 'volcano', 'cyan', 'lime'];\n\nconst ShopTag = () => {\n  const { t } = useTranslation();\n  const dispatch = useDispatch();\n  const navigate = useNavigate();\n  const [restore, setRestore] = useState(null);\n  const { setIsModalVisible } = useContext(Context);\n  const [loadingBtn, setLoadingBtn] = useState(false);\n  const [role, setRole] = useState('published');\n  const { activeMenu } = useSelector((state) => state.menu, shallowEqual);\n  const { shopTag, meta, loading, params } = useSelector(\n    (state) => state.shopTag,\n    shallowEqual\n  );\n  const [id, setId] = useState(null);\n  const data = activeMenu.data;\n  const immutable = data?.role || role;\n  const paramsData = {\n    sort: data?.sort,\n    column: data?.column,\n    perPage: data?.perPage,\n    page: data?.page,\n    status: immutable === 'deleted_at' ? null : immutable,\n    deleted_at: immutable === 'deleted_at' ? immutable : null,\n  };\n  const [columns, setColumns] = useState([\n    {\n      title: t('id'),\n      dataIndex: 'id',\n      key: 'id',\n      sorter: true,\n      is_show: true,\n    },\n    {\n      title: t('title'),\n      dataIndex: 'title',\n      key: 'title',\n      is_show: true,\n      render: (_, row) => row.translation?.title,\n    },\n    {\n      title: t('translations'),\n      dataIndex: 'locales',\n      is_show: true,\n      render: (_, row) => (\n        <Space>\n          {row.locales?.map((item, index) => (\n            <Tag\n              key={`${row.id}-locale-${index}`}\n              color={[colors[index]]}\n              className='text-uppercase'\n            >\n              {item}\n            </Tag>\n          ))}\n        </Space>\n      ),\n    },\n    {\n      title: t('image'),\n      dataIndex: 'img',\n      key: 'img',\n      is_show: true,\n      render: (img, row) => {\n        return (\n          <Image\n            src={!row.deleted_at ? IMG_URL + img : 'https://fakeimg.pl/640x360'}\n            alt='img_gallery'\n            width={100}\n            className='rounded'\n            preview\n            placeholder\n          />\n        );\n      },\n    },\n    {\n      title: t('created.at'),\n      dataIndex: 'created_at',\n      key: 'created_at',\n      is_show: true,\n      render: (created_at) => moment(created_at).format('DD/MM/YYYY'),\n    },\n    {\n      title: t('options'),\n      key: 'options',\n      dataIndex: 'options',\n      is_show: true,\n      render: (_, row) => (\n        <Space>\n          <Button\n            type='primary'\n            icon={<EditOutlined />}\n            onClick={() => goToEdit(row)}\n            disabled={row.deleted_at}\n          />\n          <Button\n            icon={<CopyOutlined />}\n            onClick={() => goToClone(row)}\n            disabled={row.deleted_at}\n          />\n          <DeleteButton\n            disabled={row.deleted_at}\n            icon={<DeleteOutlined />}\n            onClick={() => {\n              setIsModalVisible(true);\n              setId([row.id]);\n            }}\n          />\n        </Space>\n      ),\n    },\n  ]);\n\n  const goToAddBanners = () => {\n    dispatch(\n      addMenu({\n        id: 'shop-tag/add',\n        url: 'shop-tag/add',\n        name: t('add.shop.tag'),\n      })\n    );\n    navigate('/shop-tag/add');\n  };\n\n  const goToEdit = (row) => {\n    dispatch(\n      addMenu({\n        url: `shop-tag/${row.id}`,\n        id: 'shop_tag_edit',\n        name: t('edit.shop.tag'),\n      })\n    );\n    navigate(`/shop-tag/${row.id}`);\n  };\n\n  const goToClone = (row) => {\n    dispatch(\n      addMenu({\n        url: `shop-tag/clone/${row.id}`,\n        id: 'shop_tag_clone',\n        name: t('clone.shop.tag'),\n      })\n    );\n    navigate(`/shop-tag/clone/${row.id}`);\n  };\n\n  const tagDelete = () => {\n    setLoadingBtn(true);\n    const params = {\n      ...Object.assign(\n        {},\n        ...id.map((item, index) => ({\n          [`ids[${index}]`]: item,\n        }))\n      ),\n    };\n    shopTagService\n      .delete(params)\n      .then(() => {\n        dispatch(fetchShopTag());\n        toast.success(t('successfully.deleted'));\n        setIsModalVisible(false);\n      })\n      .finally(() => {\n        setLoadingBtn(false);\n      });\n  };\n\n  const tagRestoreAll = () => {\n    setLoadingBtn(true);\n    shopTagService\n      .restoreAll()\n      .then(() => {\n        dispatch(fetchShopTag(paramsData));\n        toast.success(t('successfully.deleted'));\n      })\n      .finally(() => {\n        setRestore(null);\n        setLoadingBtn(false);\n      });\n  };\n\n  const tagDropAll = () => {\n    setLoadingBtn(true);\n    shopTagService\n      .dropAll()\n      .then(() => {\n        dispatch(fetchShopTag());\n        toast.success(t('successfully.deleted'));\n      })\n      .finally(() => {\n        setRestore(null);\n        setLoadingBtn(false);\n      });\n  };\n\n  useDidUpdate(() => {\n    dispatch(fetchShopTag(paramsData));\n  }, [activeMenu.data]);\n\n  useEffect(() => {\n    if (activeMenu.refetch) {\n      dispatch(fetchShopTag(paramsData));\n      dispatch(disableRefetch(activeMenu));\n    }\n  }, [activeMenu.refetch]);\n\n  function onChangePagination(pagination, filter, sorter) {\n    const { pageSize: perPage, current: page } = pagination;\n    const { field: column, order } = sorter;\n    const sort = formatSortType(order);\n    dispatch(\n      setMenuData({\n        activeMenu,\n        data: { ...activeMenu.data, perPage, page, column, sort },\n      })\n    );\n  }\n\n  const rowSelection = {\n    selectedRowKeys: id,\n    onChange: (key) => {\n      setId(key);\n    },\n  };\n\n  const allDelete = () => {\n    if (id === null || id.length === 0) {\n      toast.warning(t('select.the.product'));\n    } else {\n      setIsModalVisible(true);\n    }\n  };\n\n  const handleFilter = (items) => {\n    const data = activeMenu.data;\n    dispatch(\n      setMenuData({\n        activeMenu,\n        data: { ...data, ...items },\n      })\n    );\n  };\n\n  return (\n    <Card\n      title={t('shop.tags')}\n      extra={\n        <Space wrap>\n          <Button\n            type='primary'\n            icon={<PlusCircleOutlined />}\n            onClick={goToAddBanners}\n          >\n            {t('add.tag')}\n          </Button>\n\n          {immutable === 'published' ? (\n            <DeleteButton size='' onClick={() => setRestore({ delete: true })}>\n              {t('delete.all')}\n            </DeleteButton>\n          ) : (\n            <DeleteButton\n              size=''\n              icon={<FaTrashRestoreAlt className='mr-2' />}\n              onClick={() => setRestore({ restore: true })}\n            >\n              {t('restore.all')}\n            </DeleteButton>\n          )}\n\n          {immutable !== 'deleted_at' && (\n            <DeleteButton size='' onClick={allDelete}>\n              {t('delete.selected')}\n            </DeleteButton>\n          )}\n\n          <FilterColumns setColumns={setColumns} columns={columns} />\n        </Space>\n      }\n    >\n      <Tabs\n        className='mt-3'\n        activeKey={immutable}\n        onChange={(key) => {\n          handleFilter({ role: key, page: 1 });\n          setRole(key);\n        }}\n        type='card'\n      >\n        {roles.map((item) => (\n          <TabPane tab={t(item)} key={item} />\n        ))}\n      </Tabs>\n      <Table\n        scroll={{ x: true }}\n        rowSelection={rowSelection}\n        columns={columns?.filter((item) => item.is_show)}\n        dataSource={shopTag}\n        pagination={{\n          pageSize: params.perPage,\n          page: activeMenu.data?.page || 1,\n          total: meta.total,\n          defaultCurrent: activeMenu.data?.page,\n          current: activeMenu.data?.page,\n        }}\n        rowKey={(record) => record.id}\n        loading={loading}\n        onChange={onChangePagination}\n      />\n      <CustomModal\n        click={tagDelete}\n        text={t('delete')}\n        loading={loadingBtn}\n        setText={setId}\n      />\n      {restore && (\n        <ResultModal\n          open={restore}\n          handleCancel={() => setRestore(null)}\n          click={restore.restore ? tagRestoreAll : tagDropAll}\n          text={restore.restore ? t('restore.modal.text') : t('read.carefully')}\n          subTitle={restore.restore ? '' : t('confirm.deletion')}\n          loading={loadingBtn}\n          setText={setId}\n        />\n      )}\n    </Card>\n  );\n};\n\nexport default ShopTag;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,UAAU,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC9D,SAASC,MAAM,EAAEC,IAAI,EAAEC,KAAK,EAAEC,KAAK,EAAEC,KAAK,EAAEC,IAAI,EAAEC,GAAG,QAAQ,MAAM;AACnE,SAASC,OAAO,QAAQ,0BAA0B;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SACEC,YAAY,EACZC,cAAc,EACdC,YAAY,EACZC,kBAAkB,QACb,mBAAmB;AAC1B,OAAOC,WAAW,MAAM,wBAAwB;AAChD,SAASC,OAAO,QAAQ,uBAAuB;AAC/C,SAASC,YAAY,EAAEC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACpE,SAASC,OAAO,EAAEC,cAAc,EAAEC,WAAW,QAAQ,yBAAyB;AAC9E,OAAOC,cAAc,MAAM,wBAAwB;AACnD,SAASC,YAAY,QAAQ,4BAA4B;AACzD,SAASC,KAAK,QAAQ,gBAAgB;AACtC,SAASC,cAAc,QAAQ,eAAe;AAC9C,OAAOC,YAAY,MAAM,gCAAgC;AACzD,OAAOC,aAAa,MAAM,gCAAgC;AAC1D,OAAOC,WAAW,MAAM,+BAA+B;AACvD,SAASC,iBAAiB,QAAQ,gBAAgB;AAClD,OAAOC,YAAY,MAAM,4BAA4B;AACrD,OAAOC,MAAM,MAAM,QAAQ;AAC3B,OAAOC,cAAc,MAAM,8BAA8B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1D,MAAMC,KAAK,GAAG,CAAC,WAAW,EAAE,YAAY,CAAC;AACzC,MAAM;EAAEC;AAAQ,CAAC,GAAG9B,IAAI;AACxB,MAAM+B,MAAM,GAAG,CAAC,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,MAAM,CAAC;AAEjE,MAAMC,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,gBAAA,EAAAC,iBAAA,EAAAC,iBAAA;EACpB,MAAM;IAAEC;EAAE,CAAC,GAAGlB,cAAc,CAAC,CAAC;EAC9B,MAAMmB,QAAQ,GAAG3B,WAAW,CAAC,CAAC;EAC9B,MAAM4B,QAAQ,GAAGpC,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACqC,OAAO,EAAEC,UAAU,CAAC,GAAG/C,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM;IAAEgD;EAAkB,CAAC,GAAGlD,UAAU,CAACiB,OAAO,CAAC;EACjD,MAAM,CAACkC,UAAU,EAAEC,aAAa,CAAC,GAAGlD,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACmD,IAAI,EAAEC,OAAO,CAAC,GAAGpD,QAAQ,CAAC,WAAW,CAAC;EAC7C,MAAM;IAAEqD;EAAW,CAAC,GAAGnC,WAAW,CAAEoC,KAAK,IAAKA,KAAK,CAACC,IAAI,EAAEvC,YAAY,CAAC;EACvE,MAAM;IAAEwC,OAAO;IAAEC,IAAI;IAAEC,OAAO;IAAEC;EAAO,CAAC,GAAGzC,WAAW,CACnDoC,KAAK,IAAKA,KAAK,CAACE,OAAO,EACxBxC,YACF,CAAC;EACD,MAAM,CAAC4C,EAAE,EAAEC,KAAK,CAAC,GAAG7D,QAAQ,CAAC,IAAI,CAAC;EAClC,MAAM8D,IAAI,GAAGT,UAAU,CAACS,IAAI;EAC5B,MAAMC,SAAS,GAAG,CAAAD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEX,IAAI,KAAIA,IAAI;EACpC,MAAMa,UAAU,GAAG;IACjBC,IAAI,EAAEH,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEG,IAAI;IAChBC,MAAM,EAAEJ,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEI,MAAM;IACpBC,OAAO,EAAEL,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEK,OAAO;IACtBC,IAAI,EAAEN,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEM,IAAI;IAChBC,MAAM,EAAEN,SAAS,KAAK,YAAY,GAAG,IAAI,GAAGA,SAAS;IACrDO,UAAU,EAAEP,SAAS,KAAK,YAAY,GAAGA,SAAS,GAAG;EACvD,CAAC;EACD,MAAM,CAACQ,OAAO,EAAEC,UAAU,CAAC,GAAGxE,QAAQ,CAAC,CACrC;IACEyE,KAAK,EAAE9B,CAAC,CAAC,IAAI,CAAC;IACd+B,SAAS,EAAE,IAAI;IACfC,GAAG,EAAE,IAAI;IACTC,MAAM,EAAE,IAAI;IACZC,OAAO,EAAE;EACX,CAAC,EACD;IACEJ,KAAK,EAAE9B,CAAC,CAAC,OAAO,CAAC;IACjB+B,SAAS,EAAE,OAAO;IAClBC,GAAG,EAAE,OAAO;IACZE,OAAO,EAAE,IAAI;IACbC,MAAM,EAAEA,CAACC,CAAC,EAAEC,GAAG;MAAA,IAAAC,gBAAA;MAAA,QAAAA,gBAAA,GAAKD,GAAG,CAACE,WAAW,cAAAD,gBAAA,uBAAfA,gBAAA,CAAiBR,KAAK;IAAA;EAC5C,CAAC,EACD;IACEA,KAAK,EAAE9B,CAAC,CAAC,cAAc,CAAC;IACxB+B,SAAS,EAAE,SAAS;IACpBG,OAAO,EAAE,IAAI;IACbC,MAAM,EAAEA,CAACC,CAAC,EAAEC,GAAG;MAAA,IAAAG,YAAA;MAAA,oBACbjD,OAAA,CAAC9B,KAAK;QAAAgF,QAAA,GAAAD,YAAA,GACHH,GAAG,CAACK,OAAO,cAAAF,YAAA,uBAAXA,YAAA,CAAaG,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBAC5BtD,OAAA,CAAC3B,GAAG;UAEFkF,KAAK,EAAE,CAACpD,MAAM,CAACmD,KAAK,CAAC,CAAE;UACvBE,SAAS,EAAC,gBAAgB;UAAAN,QAAA,EAEzBG;QAAI,GAJC,GAAEP,GAAG,CAACpB,EAAG,WAAU4B,KAAM,EAAC;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAK7B,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;EAEZ,CAAC,EACD;IACErB,KAAK,EAAE9B,CAAC,CAAC,OAAO,CAAC;IACjB+B,SAAS,EAAE,KAAK;IAChBC,GAAG,EAAE,KAAK;IACVE,OAAO,EAAE,IAAI;IACbC,MAAM,EAAEA,CAACiB,GAAG,EAAEf,GAAG,KAAK;MACpB,oBACE9C,OAAA,CAAC/B,KAAK;QACJ6F,GAAG,EAAE,CAAChB,GAAG,CAACV,UAAU,GAAG9D,OAAO,GAAGuF,GAAG,GAAG,4BAA6B;QACpEE,GAAG,EAAC,aAAa;QACjBC,KAAK,EAAE,GAAI;QACXR,SAAS,EAAC,SAAS;QACnBS,OAAO;QACPC,WAAW;MAAA;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ,CAAC;IAEN;EACF,CAAC,EACD;IACErB,KAAK,EAAE9B,CAAC,CAAC,YAAY,CAAC;IACtB+B,SAAS,EAAE,YAAY;IACvBC,GAAG,EAAE,YAAY;IACjBE,OAAO,EAAE,IAAI;IACbC,MAAM,EAAGuB,UAAU,IAAKtE,MAAM,CAACsE,UAAU,CAAC,CAACC,MAAM,CAAC,YAAY;EAChE,CAAC,EACD;IACE7B,KAAK,EAAE9B,CAAC,CAAC,SAAS,CAAC;IACnBgC,GAAG,EAAE,SAAS;IACdD,SAAS,EAAE,SAAS;IACpBG,OAAO,EAAE,IAAI;IACbC,MAAM,EAAEA,CAACC,CAAC,EAAEC,GAAG,kBACb9C,OAAA,CAAC9B,KAAK;MAAAgF,QAAA,gBACJlD,OAAA,CAACjC,MAAM;QACLsG,IAAI,EAAC,SAAS;QACdC,IAAI,eAAEtE,OAAA,CAACtB,YAAY;UAAA+E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvBW,OAAO,EAAEA,CAAA,KAAMC,QAAQ,CAAC1B,GAAG,CAAE;QAC7B2B,QAAQ,EAAE3B,GAAG,CAACV;MAAW;QAAAqB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B,CAAC,eACF5D,OAAA,CAACjC,MAAM;QACLuG,IAAI,eAAEtE,OAAA,CAACxB,YAAY;UAAAiF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvBW,OAAO,EAAEA,CAAA,KAAMG,SAAS,CAAC5B,GAAG,CAAE;QAC9B2B,QAAQ,EAAE3B,GAAG,CAACV;MAAW;QAAAqB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B,CAAC,eACF5D,OAAA,CAACR,YAAY;QACXiF,QAAQ,EAAE3B,GAAG,CAACV,UAAW;QACzBkC,IAAI,eAAEtE,OAAA,CAACvB,cAAc;UAAAgF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACzBW,OAAO,EAAEA,CAAA,KAAM;UACbzD,iBAAiB,CAAC,IAAI,CAAC;UACvBa,KAAK,CAAC,CAACmB,GAAG,CAACpB,EAAE,CAAC,CAAC;QACjB;MAAE;QAAA+B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG;EAEX,CAAC,CACF,CAAC;EAEF,MAAMe,cAAc,GAAGA,CAAA,KAAM;IAC3BjE,QAAQ,CACNzB,OAAO,CAAC;MACNyC,EAAE,EAAE,cAAc;MAClBkD,GAAG,EAAE,cAAc;MACnBC,IAAI,EAAEpE,CAAC,CAAC,cAAc;IACxB,CAAC,CACH,CAAC;IACDE,QAAQ,CAAC,eAAe,CAAC;EAC3B,CAAC;EAED,MAAM6D,QAAQ,GAAI1B,GAAG,IAAK;IACxBpC,QAAQ,CACNzB,OAAO,CAAC;MACN2F,GAAG,EAAG,YAAW9B,GAAG,CAACpB,EAAG,EAAC;MACzBA,EAAE,EAAE,eAAe;MACnBmD,IAAI,EAAEpE,CAAC,CAAC,eAAe;IACzB,CAAC,CACH,CAAC;IACDE,QAAQ,CAAE,aAAYmC,GAAG,CAACpB,EAAG,EAAC,CAAC;EACjC,CAAC;EAED,MAAMgD,SAAS,GAAI5B,GAAG,IAAK;IACzBpC,QAAQ,CACNzB,OAAO,CAAC;MACN2F,GAAG,EAAG,kBAAiB9B,GAAG,CAACpB,EAAG,EAAC;MAC/BA,EAAE,EAAE,gBAAgB;MACpBmD,IAAI,EAAEpE,CAAC,CAAC,gBAAgB;IAC1B,CAAC,CACH,CAAC;IACDE,QAAQ,CAAE,mBAAkBmC,GAAG,CAACpB,EAAG,EAAC,CAAC;EACvC,CAAC;EAED,MAAMoD,SAAS,GAAGA,CAAA,KAAM;IACtB9D,aAAa,CAAC,IAAI,CAAC;IACnB,MAAMS,MAAM,GAAG;MACb,GAAGsD,MAAM,CAACC,MAAM,CACd,CAAC,CAAC,EACF,GAAGtD,EAAE,CAAC0B,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,MAAM;QAC1B,CAAE,OAAMA,KAAM,GAAE,GAAGD;MACrB,CAAC,CAAC,CACJ;IACF,CAAC;IACDjE,cAAc,CACX6F,MAAM,CAACxD,MAAM,CAAC,CACdyD,IAAI,CAAC,MAAM;MACVxE,QAAQ,CAACrB,YAAY,CAAC,CAAC,CAAC;MACxBC,KAAK,CAAC6F,OAAO,CAAC1E,CAAC,CAAC,sBAAsB,CAAC,CAAC;MACxCK,iBAAiB,CAAC,KAAK,CAAC;IAC1B,CAAC,CAAC,CACDsE,OAAO,CAAC,MAAM;MACbpE,aAAa,CAAC,KAAK,CAAC;IACtB,CAAC,CAAC;EACN,CAAC;EAED,MAAMqE,aAAa,GAAGA,CAAA,KAAM;IAC1BrE,aAAa,CAAC,IAAI,CAAC;IACnB5B,cAAc,CACXkG,UAAU,CAAC,CAAC,CACZJ,IAAI,CAAC,MAAM;MACVxE,QAAQ,CAACrB,YAAY,CAACyC,UAAU,CAAC,CAAC;MAClCxC,KAAK,CAAC6F,OAAO,CAAC1E,CAAC,CAAC,sBAAsB,CAAC,CAAC;IAC1C,CAAC,CAAC,CACD2E,OAAO,CAAC,MAAM;MACbvE,UAAU,CAAC,IAAI,CAAC;MAChBG,aAAa,CAAC,KAAK,CAAC;IACtB,CAAC,CAAC;EACN,CAAC;EAED,MAAMuE,UAAU,GAAGA,CAAA,KAAM;IACvBvE,aAAa,CAAC,IAAI,CAAC;IACnB5B,cAAc,CACXoG,OAAO,CAAC,CAAC,CACTN,IAAI,CAAC,MAAM;MACVxE,QAAQ,CAACrB,YAAY,CAAC,CAAC,CAAC;MACxBC,KAAK,CAAC6F,OAAO,CAAC1E,CAAC,CAAC,sBAAsB,CAAC,CAAC;IAC1C,CAAC,CAAC,CACD2E,OAAO,CAAC,MAAM;MACbvE,UAAU,CAAC,IAAI,CAAC;MAChBG,aAAa,CAAC,KAAK,CAAC;IACtB,CAAC,CAAC;EACN,CAAC;EAEDpB,YAAY,CAAC,MAAM;IACjBc,QAAQ,CAACrB,YAAY,CAACyC,UAAU,CAAC,CAAC;EACpC,CAAC,EAAE,CAACX,UAAU,CAACS,IAAI,CAAC,CAAC;EAErB/D,SAAS,CAAC,MAAM;IACd,IAAIsD,UAAU,CAACsE,OAAO,EAAE;MACtB/E,QAAQ,CAACrB,YAAY,CAACyC,UAAU,CAAC,CAAC;MAClCpB,QAAQ,CAACxB,cAAc,CAACiC,UAAU,CAAC,CAAC;IACtC;EACF,CAAC,EAAE,CAACA,UAAU,CAACsE,OAAO,CAAC,CAAC;EAExB,SAASC,kBAAkBA,CAACC,UAAU,EAAEC,MAAM,EAAElD,MAAM,EAAE;IACtD,MAAM;MAAEmD,QAAQ,EAAE5D,OAAO;MAAE6D,OAAO,EAAE5D;IAAK,CAAC,GAAGyD,UAAU;IACvD,MAAM;MAAEI,KAAK,EAAE/D,MAAM;MAAEgE;IAAM,CAAC,GAAGtD,MAAM;IACvC,MAAMX,IAAI,GAAGjC,cAAc,CAACkG,KAAK,CAAC;IAClCtF,QAAQ,CACNvB,WAAW,CAAC;MACVgC,UAAU;MACVS,IAAI,EAAE;QAAE,GAAGT,UAAU,CAACS,IAAI;QAAEK,OAAO;QAAEC,IAAI;QAAEF,MAAM;QAAED;MAAK;IAC1D,CAAC,CACH,CAAC;EACH;EAEA,MAAMkE,YAAY,GAAG;IACnBC,eAAe,EAAExE,EAAE;IACnByE,QAAQ,EAAG1D,GAAG,IAAK;MACjBd,KAAK,CAACc,GAAG,CAAC;IACZ;EACF,CAAC;EAED,MAAM2D,SAAS,GAAGA,CAAA,KAAM;IACtB,IAAI1E,EAAE,KAAK,IAAI,IAAIA,EAAE,CAAC2E,MAAM,KAAK,CAAC,EAAE;MAClC/G,KAAK,CAACgH,OAAO,CAAC7F,CAAC,CAAC,oBAAoB,CAAC,CAAC;IACxC,CAAC,MAAM;MACLK,iBAAiB,CAAC,IAAI,CAAC;IACzB;EACF,CAAC;EAED,MAAMyF,YAAY,GAAIC,KAAK,IAAK;IAC9B,MAAM5E,IAAI,GAAGT,UAAU,CAACS,IAAI;IAC5BlB,QAAQ,CACNvB,WAAW,CAAC;MACVgC,UAAU;MACVS,IAAI,EAAE;QAAE,GAAGA,IAAI;QAAE,GAAG4E;MAAM;IAC5B,CAAC,CACH,CAAC;EACH,CAAC;EAED,oBACExG,OAAA,CAAChC,IAAI;IACHuE,KAAK,EAAE9B,CAAC,CAAC,WAAW,CAAE;IACtBgG,KAAK,eACHzG,OAAA,CAAC9B,KAAK;MAACwI,IAAI;MAAAxD,QAAA,gBACTlD,OAAA,CAACjC,MAAM;QACLsG,IAAI,EAAC,SAAS;QACdC,IAAI,eAAEtE,OAAA,CAACrB,kBAAkB;UAAA8E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAC7BW,OAAO,EAAEI,cAAe;QAAAzB,QAAA,EAEvBzC,CAAC,CAAC,SAAS;MAAC;QAAAgD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CAAC,EAER/B,SAAS,KAAK,WAAW,gBACxB7B,OAAA,CAACR,YAAY;QAACmH,IAAI,EAAC,EAAE;QAACpC,OAAO,EAAEA,CAAA,KAAM1D,UAAU,CAAC;UAAEoE,MAAM,EAAE;QAAK,CAAC,CAAE;QAAA/B,QAAA,EAC/DzC,CAAC,CAAC,YAAY;MAAC;QAAAgD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,gBAEf5D,OAAA,CAACR,YAAY;QACXmH,IAAI,EAAC,EAAE;QACPrC,IAAI,eAAEtE,OAAA,CAACL,iBAAiB;UAAC6D,SAAS,EAAC;QAAM;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAC7CW,OAAO,EAAEA,CAAA,KAAM1D,UAAU,CAAC;UAAED,OAAO,EAAE;QAAK,CAAC,CAAE;QAAAsC,QAAA,EAE5CzC,CAAC,CAAC,aAAa;MAAC;QAAAgD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CACf,EAEA/B,SAAS,KAAK,YAAY,iBACzB7B,OAAA,CAACR,YAAY;QAACmH,IAAI,EAAC,EAAE;QAACpC,OAAO,EAAE6B,SAAU;QAAAlD,QAAA,EACtCzC,CAAC,CAAC,iBAAiB;MAAC;QAAAgD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CACf,eAED5D,OAAA,CAACP,aAAa;QAAC6C,UAAU,EAAEA,UAAW;QAACD,OAAO,EAAEA;MAAQ;QAAAoB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtD,CACR;IAAAV,QAAA,gBAEDlD,OAAA,CAAC5B,IAAI;MACHoF,SAAS,EAAC,MAAM;MAChBoD,SAAS,EAAE/E,SAAU;MACrBsE,QAAQ,EAAG1D,GAAG,IAAK;QACjB8D,YAAY,CAAC;UAAEtF,IAAI,EAAEwB,GAAG;UAAEP,IAAI,EAAE;QAAE,CAAC,CAAC;QACpChB,OAAO,CAACuB,GAAG,CAAC;MACd,CAAE;MACF4B,IAAI,EAAC,MAAM;MAAAnB,QAAA,EAEVjD,KAAK,CAACmD,GAAG,CAAEC,IAAI,iBACdrD,OAAA,CAACE,OAAO;QAAC2G,GAAG,EAAEpG,CAAC,CAAC4C,IAAI;MAAE,GAAMA,IAAI;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CACpC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eACP5D,OAAA,CAAC7B,KAAK;MACJ2I,MAAM,EAAE;QAAEC,CAAC,EAAE;MAAK,CAAE;MACpBd,YAAY,EAAEA,YAAa;MAC3B5D,OAAO,EAAEA,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEuD,MAAM,CAAEvC,IAAI,IAAKA,IAAI,CAACV,OAAO,CAAE;MACjDqE,UAAU,EAAE1F,OAAQ;MACpBqE,UAAU,EAAE;QACVE,QAAQ,EAAEpE,MAAM,CAACQ,OAAO;QACxBC,IAAI,EAAE,EAAA5B,gBAAA,GAAAa,UAAU,CAACS,IAAI,cAAAtB,gBAAA,uBAAfA,gBAAA,CAAiB4B,IAAI,KAAI,CAAC;QAChC+E,KAAK,EAAE1F,IAAI,CAAC0F,KAAK;QACjBC,cAAc,GAAA3G,iBAAA,GAAEY,UAAU,CAACS,IAAI,cAAArB,iBAAA,uBAAfA,iBAAA,CAAiB2B,IAAI;QACrC4D,OAAO,GAAAtF,iBAAA,GAAEW,UAAU,CAACS,IAAI,cAAApB,iBAAA,uBAAfA,iBAAA,CAAiB0B;MAC5B,CAAE;MACFiF,MAAM,EAAGC,MAAM,IAAKA,MAAM,CAAC1F,EAAG;MAC9BF,OAAO,EAAEA,OAAQ;MACjB2E,QAAQ,EAAET;IAAmB;MAAAjC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9B,CAAC,eACF5D,OAAA,CAACpB,WAAW;MACVyI,KAAK,EAAEvC,SAAU;MACjBwC,IAAI,EAAE7G,CAAC,CAAC,QAAQ,CAAE;MAClBe,OAAO,EAAET,UAAW;MACpBwG,OAAO,EAAE5F;IAAM;MAAA8B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChB,CAAC,EACDhD,OAAO,iBACNZ,OAAA,CAACN,WAAW;MACV8H,IAAI,EAAE5G,OAAQ;MACd6G,YAAY,EAAEA,CAAA,KAAM5G,UAAU,CAAC,IAAI,CAAE;MACrCwG,KAAK,EAAEzG,OAAO,CAACA,OAAO,GAAGyE,aAAa,GAAGE,UAAW;MACpD+B,IAAI,EAAE1G,OAAO,CAACA,OAAO,GAAGH,CAAC,CAAC,oBAAoB,CAAC,GAAGA,CAAC,CAAC,gBAAgB,CAAE;MACtEiH,QAAQ,EAAE9G,OAAO,CAACA,OAAO,GAAG,EAAE,GAAGH,CAAC,CAAC,kBAAkB,CAAE;MACvDe,OAAO,EAAET,UAAW;MACpBwG,OAAO,EAAE5F;IAAM;MAAA8B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChB,CACF;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEX,CAAC;AAACvD,EAAA,CAzUID,OAAO;EAAA,QACGb,cAAc,EACXR,WAAW,EACXR,WAAW,EAKLS,WAAW,EACSA,WAAW,EA2LtDY,YAAY;AAAA;AAAA+H,EAAA,GApMRvH,OAAO;AA2Ub,eAAeA,OAAO;AAAC,IAAAuH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}