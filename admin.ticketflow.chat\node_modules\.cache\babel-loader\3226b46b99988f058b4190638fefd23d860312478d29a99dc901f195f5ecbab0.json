{"ast": null, "code": "import axios from 'axios';\nimport { notification } from 'antd';\nimport { api_url } from '../configs/app-global';\nimport { store } from '../redux/store';\nimport { clearUser } from '../redux/slices/auth';\nimport i18n from '../configs/i18next';\nimport { toast } from 'react-toastify';\nconst service = axios.create({\n  baseURL: api_url,\n  timeout: 30000 // Increased to 30 seconds for better reliability\n});\n\n// Config\nconst TOKEN_PAYLOAD_KEY = 'authorization';\nconst AUTH_TOKEN = 'token';\nconst AUTH_TOKEN_TYPE = 'Bearer';\n\n// API Request interceptor\nservice.interceptors.request.use(config => {\n  const access_token = localStorage.getItem(AUTH_TOKEN);\n  if (access_token) {\n    config.headers[TOKEN_PAYLOAD_KEY] = AUTH_TOKEN_TYPE + ' ' + access_token;\n  }\n  if (config.method === 'get') {\n    config.params = {\n      lang: i18n.language,\n      ...config.params\n    };\n  }\n  return config;\n}, error => {\n  // Do something with request error here\n  notification.error({\n    message: 'Error'\n  });\n  Promise.reject(error);\n});\n\n// API respone interceptor\nservice.interceptors.response.use(response => {\n  return response.data;\n}, error => {\n  var _error$response, _error$response$data;\n  let notificationParam = {\n    message: i18n.t(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || 'network.error')\n  };\n\n  // Check if error.response exists before accessing its properties\n  if (error.response) {\n    var _error$response$data4;\n    // Remove token and redirect\n    if (error.response.status === 403 || error.response.status === 401) {\n      localStorage.removeItem(AUTH_TOKEN);\n      store.dispatch(clearUser());\n    }\n    if (error.response.status === 508) {\n      var _error$response$data2;\n      notificationParam.message = ((_error$response$data2 = error.response.data) === null || _error$response$data2 === void 0 ? void 0 : _error$response$data2.message) || 'server.error';\n    }\n    if (error.response.status === 500) {\n      var _error$response$data3;\n      notificationParam.message = ((_error$response$data3 = error.response.data) === null || _error$response$data3 === void 0 ? void 0 : _error$response$data3.message) || 'internal.server.error';\n    }\n    if ((_error$response$data4 = error.response.data) !== null && _error$response$data4 !== void 0 && _error$response$data4.params) {\n      if (Object.values(error.response.data.params)[0]) {\n        notificationParam.message = Object.values(error.response.data.params)[0].at(0);\n      }\n    }\n    toast.error(notificationParam.message, {\n      toastId: error.response.status\n    });\n  } else {\n    // Handle network errors or other errors without response\n    notificationParam.message = error.message || 'network.connection.error';\n    toast.error(notificationParam.message, {\n      toastId: 'network-error'\n    });\n  }\n  return Promise.reject(error);\n});\nexport default service;", "map": {"version": 3, "names": ["axios", "notification", "api_url", "store", "clearUser", "i18n", "toast", "service", "create", "baseURL", "timeout", "TOKEN_PAYLOAD_KEY", "AUTH_TOKEN", "AUTH_TOKEN_TYPE", "interceptors", "request", "use", "config", "access_token", "localStorage", "getItem", "headers", "method", "params", "lang", "language", "error", "message", "Promise", "reject", "response", "data", "_error$response", "_error$response$data", "notificationParam", "t", "_error$response$data4", "status", "removeItem", "dispatch", "_error$response$data2", "_error$response$data3", "Object", "values", "at", "toastId"], "sources": ["C:/OSPanel/home/<USER>/src/services/request.js"], "sourcesContent": ["import axios from 'axios';\nimport { notification } from 'antd';\nimport { api_url } from '../configs/app-global';\nimport { store } from '../redux/store';\nimport { clearUser } from '../redux/slices/auth';\nimport i18n from '../configs/i18next';\nimport { toast } from 'react-toastify';\n\nconst service = axios.create({\n  baseURL: api_url,\n  timeout: 30000, // Increased to 30 seconds for better reliability\n});\n\n// Config\nconst TOKEN_PAYLOAD_KEY = 'authorization';\nconst AUTH_TOKEN = 'token';\nconst AUTH_TOKEN_TYPE = 'Bearer';\n\n// API Request interceptor\nservice.interceptors.request.use(\n  (config) => {\n    const access_token = localStorage.getItem(AUTH_TOKEN);\n\n    if (access_token) {\n      config.headers[TOKEN_PAYLOAD_KEY] = AUTH_TOKEN_TYPE + ' ' + access_token;\n    }\n    if (config.method === 'get') {\n      config.params = { lang: i18n.language, ...config.params };\n    }\n\n    return config;\n  },\n  (error) => {\n    // Do something with request error here\n    notification.error({\n      message: 'Error',\n    });\n    Promise.reject(error);\n  }\n);\n\n// API respone interceptor\nservice.interceptors.response.use(\n  (response) => {\n    return response.data;\n  },\n  (error) => {\n    let notificationParam = {\n      message: i18n.t(error.response?.data?.message || 'network.error'),\n    };\n\n    // Check if error.response exists before accessing its properties\n    if (error.response) {\n      // Remove token and redirect\n      if (error.response.status === 403 || error.response.status === 401) {\n        localStorage.removeItem(AUTH_TOKEN);\n        store.dispatch(clearUser());\n      }\n\n      if (error.response.status === 508) {\n        notificationParam.message = error.response.data?.message || 'server.error';\n      }\n\n      if (error.response.status === 500) {\n        notificationParam.message = error.response.data?.message || 'internal.server.error';\n      }\n\n      if(error.response.data?.params) {\n        if(Object.values(error.response.data.params)[0]){\n          notificationParam.message = Object.values(error.response.data.params)[0].at(0)\n        }\n      }\n\n      toast.error(notificationParam.message, {\n        toastId: error.response.status,\n      });\n    } else {\n      // Handle network errors or other errors without response\n      notificationParam.message = error.message || 'network.connection.error';\n      toast.error(notificationParam.message, {\n        toastId: 'network-error',\n      });\n    }\n\n    return Promise.reject(error);\n  }\n);\n\nexport default service;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,YAAY,QAAQ,MAAM;AACnC,SAASC,OAAO,QAAQ,uBAAuB;AAC/C,SAASC,KAAK,QAAQ,gBAAgB;AACtC,SAASC,SAAS,QAAQ,sBAAsB;AAChD,OAAOC,IAAI,MAAM,oBAAoB;AACrC,SAASC,KAAK,QAAQ,gBAAgB;AAEtC,MAAMC,OAAO,GAAGP,KAAK,CAACQ,MAAM,CAAC;EAC3BC,OAAO,EAAEP,OAAO;EAChBQ,OAAO,EAAE,KAAK,CAAE;AAClB,CAAC,CAAC;;AAEF;AACA,MAAMC,iBAAiB,GAAG,eAAe;AACzC,MAAMC,UAAU,GAAG,OAAO;AAC1B,MAAMC,eAAe,GAAG,QAAQ;;AAEhC;AACAN,OAAO,CAACO,YAAY,CAACC,OAAO,CAACC,GAAG,CAC7BC,MAAM,IAAK;EACV,MAAMC,YAAY,GAAGC,YAAY,CAACC,OAAO,CAACR,UAAU,CAAC;EAErD,IAAIM,YAAY,EAAE;IAChBD,MAAM,CAACI,OAAO,CAACV,iBAAiB,CAAC,GAAGE,eAAe,GAAG,GAAG,GAAGK,YAAY;EAC1E;EACA,IAAID,MAAM,CAACK,MAAM,KAAK,KAAK,EAAE;IAC3BL,MAAM,CAACM,MAAM,GAAG;MAAEC,IAAI,EAAEnB,IAAI,CAACoB,QAAQ;MAAE,GAAGR,MAAM,CAACM;IAAO,CAAC;EAC3D;EAEA,OAAON,MAAM;AACf,CAAC,EACAS,KAAK,IAAK;EACT;EACAzB,YAAY,CAACyB,KAAK,CAAC;IACjBC,OAAO,EAAE;EACX,CAAC,CAAC;EACFC,OAAO,CAACC,MAAM,CAACH,KAAK,CAAC;AACvB,CACF,CAAC;;AAED;AACAnB,OAAO,CAACO,YAAY,CAACgB,QAAQ,CAACd,GAAG,CAC9Bc,QAAQ,IAAK;EACZ,OAAOA,QAAQ,CAACC,IAAI;AACtB,CAAC,EACAL,KAAK,IAAK;EAAA,IAAAM,eAAA,EAAAC,oBAAA;EACT,IAAIC,iBAAiB,GAAG;IACtBP,OAAO,EAAEtB,IAAI,CAAC8B,CAAC,CAAC,EAAAH,eAAA,GAAAN,KAAK,CAACI,QAAQ,cAAAE,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBD,IAAI,cAAAE,oBAAA,uBAApBA,oBAAA,CAAsBN,OAAO,KAAI,eAAe;EAClE,CAAC;;EAED;EACA,IAAID,KAAK,CAACI,QAAQ,EAAE;IAAA,IAAAM,qBAAA;IAClB;IACA,IAAIV,KAAK,CAACI,QAAQ,CAACO,MAAM,KAAK,GAAG,IAAIX,KAAK,CAACI,QAAQ,CAACO,MAAM,KAAK,GAAG,EAAE;MAClElB,YAAY,CAACmB,UAAU,CAAC1B,UAAU,CAAC;MACnCT,KAAK,CAACoC,QAAQ,CAACnC,SAAS,CAAC,CAAC,CAAC;IAC7B;IAEA,IAAIsB,KAAK,CAACI,QAAQ,CAACO,MAAM,KAAK,GAAG,EAAE;MAAA,IAAAG,qBAAA;MACjCN,iBAAiB,CAACP,OAAO,GAAG,EAAAa,qBAAA,GAAAd,KAAK,CAACI,QAAQ,CAACC,IAAI,cAAAS,qBAAA,uBAAnBA,qBAAA,CAAqBb,OAAO,KAAI,cAAc;IAC5E;IAEA,IAAID,KAAK,CAACI,QAAQ,CAACO,MAAM,KAAK,GAAG,EAAE;MAAA,IAAAI,qBAAA;MACjCP,iBAAiB,CAACP,OAAO,GAAG,EAAAc,qBAAA,GAAAf,KAAK,CAACI,QAAQ,CAACC,IAAI,cAAAU,qBAAA,uBAAnBA,qBAAA,CAAqBd,OAAO,KAAI,uBAAuB;IACrF;IAEA,KAAAS,qBAAA,GAAGV,KAAK,CAACI,QAAQ,CAACC,IAAI,cAAAK,qBAAA,eAAnBA,qBAAA,CAAqBb,MAAM,EAAE;MAC9B,IAAGmB,MAAM,CAACC,MAAM,CAACjB,KAAK,CAACI,QAAQ,CAACC,IAAI,CAACR,MAAM,CAAC,CAAC,CAAC,CAAC,EAAC;QAC9CW,iBAAiB,CAACP,OAAO,GAAGe,MAAM,CAACC,MAAM,CAACjB,KAAK,CAACI,QAAQ,CAACC,IAAI,CAACR,MAAM,CAAC,CAAC,CAAC,CAAC,CAACqB,EAAE,CAAC,CAAC,CAAC;MAChF;IACF;IAEAtC,KAAK,CAACoB,KAAK,CAACQ,iBAAiB,CAACP,OAAO,EAAE;MACrCkB,OAAO,EAAEnB,KAAK,CAACI,QAAQ,CAACO;IAC1B,CAAC,CAAC;EACJ,CAAC,MAAM;IACL;IACAH,iBAAiB,CAACP,OAAO,GAAGD,KAAK,CAACC,OAAO,IAAI,0BAA0B;IACvErB,KAAK,CAACoB,KAAK,CAACQ,iBAAiB,CAACP,OAAO,EAAE;MACrCkB,OAAO,EAAE;IACX,CAAC,CAAC;EACJ;EAEA,OAAOjB,OAAO,CAACC,MAAM,CAACH,KAAK,CAAC;AAC9B,CACF,CAAC;AAED,eAAenB,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}