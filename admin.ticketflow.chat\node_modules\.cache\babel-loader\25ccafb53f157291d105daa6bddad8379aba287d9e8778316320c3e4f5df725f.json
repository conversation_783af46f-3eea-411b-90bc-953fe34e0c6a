{"ast": null, "code": "var _jsxFileName = \"C:\\\\OSPanel\\\\home\\\\admin.ticketflow.chat\\\\src\\\\views\\\\order\\\\edit\\\\components\\\\products\\\\components\\\\list.js\",\n  _s = $RefreshSig$();\nimport { Card, Col, Row, Spin } from 'antd';\nimport RiveResult from 'components/rive-result';\nimport React from 'react';\nimport Meta from 'antd/lib/card/Meta';\nimport { PlusOutlined } from '@ant-design/icons';\nimport { BsFillGiftFill } from 'react-icons/bs';\nimport { useTranslation } from 'react-i18next';\nimport { shallowEqual, useSelector } from 'react-redux';\nimport { useQueryParams } from 'helpers/useQueryParams';\nimport ProductModal from './productModal';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ProductList = () => {\n  _s();\n  const {\n    t\n  } = useTranslation();\n  const queryParams = useQueryParams();\n  const {\n    products,\n    loading\n  } = useSelector(state => state.product, shallowEqual);\n  const handleOpenProductModal = uuid => {\n    queryParams.set('uuid', uuid);\n  };\n  const handleCloseProductModal = () => {\n    queryParams.reset('uuid');\n  };\n  const renderView = () => {\n    if (!(products !== null && products !== void 0 && products.length)) {\n      return /*#__PURE__*/_jsxDEV(Row, {\n        style: {\n          width: '100%'\n        },\n        children: /*#__PURE__*/_jsxDEV(Col, {\n          span: 24,\n          children: /*#__PURE__*/_jsxDEV(RiveResult, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 34,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 33,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 32,\n        columnNumber: 9\n      }, this);\n    }\n    return products === null || products === void 0 ? void 0 : products.map(item => {\n      var _item$translation, _item$translation2, _item$stocks;\n      return /*#__PURE__*/_jsxDEV(Card, {\n        className: \"products-col\",\n        cover: /*#__PURE__*/_jsxDEV(\"img\", {\n          alt: (item === null || item === void 0 ? void 0 : (_item$translation = item.translation) === null || _item$translation === void 0 ? void 0 : _item$translation.title) || t('N/A'),\n          src: (item === null || item === void 0 ? void 0 : item.img) || 'https://via.placeholder.com/150'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 44,\n          columnNumber: 11\n        }, this),\n        onClick: () => {\n          handleOpenProductModal(item === null || item === void 0 ? void 0 : item.uuid);\n        },\n        children: [/*#__PURE__*/_jsxDEV(Meta, {\n          title: (item === null || item === void 0 ? void 0 : (_item$translation2 = item.translation) === null || _item$translation2 === void 0 ? void 0 : _item$translation2.title) || t('N/A')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 53,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"preview\",\n          children: /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 55,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 54,\n          columnNumber: 9\n        }, this), item === null || item === void 0 ? void 0 : (_item$stocks = item.stocks) === null || _item$stocks === void 0 ? void 0 : _item$stocks.map((it, stockIndex) => {\n          var _it$bonus, _it$bonus2;\n          return /*#__PURE__*/_jsxDEV(\"span\", {\n            className: it !== null && it !== void 0 && it.bonus ? 'show-bonus' : 'd-none',\n            children: [/*#__PURE__*/_jsxDEV(BsFillGiftFill, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 62,\n              columnNumber: 13\n            }, this), \" \", it === null || it === void 0 ? void 0 : (_it$bonus = it.bonus) === null || _it$bonus === void 0 ? void 0 : _it$bonus.value, '+', it === null || it === void 0 ? void 0 : (_it$bonus2 = it.bonus) === null || _it$bonus2 === void 0 ? void 0 : _it$bonus2.bonus_quantity]\n          }, (it === null || it === void 0 ? void 0 : it.id) || `stock-${stockIndex}`, true, {\n            fileName: _jsxFileName,\n            lineNumber: 58,\n            columnNumber: 11\n          }, this);\n        })]\n      }, item === null || item === void 0 ? void 0 : item.id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 40,\n        columnNumber: 7\n      }, this);\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [loading && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"loader\",\n      children: /*#__PURE__*/_jsxDEV(Spin, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 74,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 73,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"products-row order-items\",\n      children: renderView()\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 77,\n      columnNumber: 7\n    }, this), !!queryParams.get('uuid') && /*#__PURE__*/_jsxDEV(ProductModal, {\n      uuid: queryParams.get('uuid'),\n      onCancel: handleCloseProductModal\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 79,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true);\n};\n_s(ProductList, \"QgFknqYV4fU6e/4R6yUNBmynlLk=\", false, function () {\n  return [useTranslation, useQueryParams, useSelector];\n});\n_c = ProductList;\nexport default ProductList;\nvar _c;\n$RefreshReg$(_c, \"ProductList\");", "map": {"version": 3, "names": ["Card", "Col", "Row", "Spin", "RiveResult", "React", "Meta", "PlusOutlined", "BsFillGiftFill", "useTranslation", "shallowEqual", "useSelector", "useQueryParams", "ProductModal", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ProductList", "_s", "t", "queryParams", "products", "loading", "state", "product", "handleOpenProductModal", "uuid", "set", "handleCloseProductModal", "reset", "render<PERSON>iew", "length", "style", "width", "children", "span", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "item", "_item$translation", "_item$translation2", "_item$stocks", "className", "cover", "alt", "translation", "title", "src", "img", "onClick", "stocks", "it", "stockIndex", "_it$bonus", "_it$bonus2", "bonus", "value", "bonus_quantity", "id", "get", "onCancel", "_c", "$RefreshReg$"], "sources": ["C:/OSPanel/home/<USER>/src/views/order/edit/components/products/components/list.js"], "sourcesContent": ["import { Card, Col, Row, Spin } from 'antd';\nimport RiveResult from 'components/rive-result';\nimport React from 'react';\nimport Meta from 'antd/lib/card/Meta';\nimport { PlusOutlined } from '@ant-design/icons';\nimport { BsFillGiftFill } from 'react-icons/bs';\nimport { useTranslation } from 'react-i18next';\nimport { shallowEqual, useSelector } from 'react-redux';\nimport { useQueryParams } from 'helpers/useQueryParams';\nimport ProductModal from './productModal';\n\nconst ProductList = () => {\n  const { t } = useTranslation();\n  const queryParams = useQueryParams();\n\n  const { products, loading } = useSelector(\n    (state) => state.product,\n    shallowEqual,\n  );\n\n  const handleOpenProductModal = (uuid) => {\n    queryParams.set('uuid', uuid);\n  };\n\n  const handleCloseProductModal = () => {\n    queryParams.reset('uuid');\n  };\n\n  const renderView = () => {\n    if (!products?.length) {\n      return (\n        <Row style={{ width: '100%' }}>\n          <Col span={24}>\n            <RiveResult />\n          </Col>\n        </Row>\n      );\n    }\n    return products?.map((item) => (\n      <Card\n        className='products-col'\n        key={item?.id}\n        cover={\n          <img\n            alt={item?.translation?.title || t('N/A')}\n            src={item?.img || 'https://via.placeholder.com/150'}\n          />\n        }\n        onClick={() => {\n          handleOpenProductModal(item?.uuid);\n        }}\n      >\n        <Meta title={item?.translation?.title || t('N/A')} />\n        <div className='preview'>\n          <PlusOutlined />\n        </div>\n        {item?.stocks?.map((it, stockIndex) => (\n          <span\n            key={it?.id || `stock-${stockIndex}`}\n            className={it?.bonus ? 'show-bonus' : 'd-none'}\n          >\n            <BsFillGiftFill /> {it?.bonus?.value}\n            {'+'}\n            {it?.bonus?.bonus_quantity}\n          </span>\n        ))}\n      </Card>\n    ));\n  };\n  return (\n    <>\n      {loading && (\n        <div className='loader'>\n          <Spin />\n        </div>\n      )}\n      <div className='products-row order-items'>{renderView()}</div>\n      {!!queryParams.get('uuid') && (\n        <ProductModal\n          uuid={queryParams.get('uuid')}\n          onCancel={handleCloseProductModal}\n        />\n      )}\n    </>\n  );\n};\n\nexport default ProductList;\n"], "mappings": ";;AAAA,SAASA,IAAI,EAAEC,GAAG,EAAEC,GAAG,EAAEC,IAAI,QAAQ,MAAM;AAC3C,OAAOC,UAAU,MAAM,wBAAwB;AAC/C,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,IAAI,MAAM,oBAAoB;AACrC,SAASC,YAAY,QAAQ,mBAAmB;AAChD,SAASC,cAAc,QAAQ,gBAAgB;AAC/C,SAASC,cAAc,QAAQ,eAAe;AAC9C,SAASC,YAAY,EAAEC,WAAW,QAAQ,aAAa;AACvD,SAASC,cAAc,QAAQ,wBAAwB;AACvD,OAAOC,YAAY,MAAM,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE1C,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxB,MAAM;IAAEC;EAAE,CAAC,GAAGX,cAAc,CAAC,CAAC;EAC9B,MAAMY,WAAW,GAAGT,cAAc,CAAC,CAAC;EAEpC,MAAM;IAAEU,QAAQ;IAAEC;EAAQ,CAAC,GAAGZ,WAAW,CACtCa,KAAK,IAAKA,KAAK,CAACC,OAAO,EACxBf,YACF,CAAC;EAED,MAAMgB,sBAAsB,GAAIC,IAAI,IAAK;IACvCN,WAAW,CAACO,GAAG,CAAC,MAAM,EAAED,IAAI,CAAC;EAC/B,CAAC;EAED,MAAME,uBAAuB,GAAGA,CAAA,KAAM;IACpCR,WAAW,CAACS,KAAK,CAAC,MAAM,CAAC;EAC3B,CAAC;EAED,MAAMC,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAI,EAACT,QAAQ,aAARA,QAAQ,eAARA,QAAQ,CAAEU,MAAM,GAAE;MACrB,oBACEjB,OAAA,CAACb,GAAG;QAAC+B,KAAK,EAAE;UAAEC,KAAK,EAAE;QAAO,CAAE;QAAAC,QAAA,eAC5BpB,OAAA,CAACd,GAAG;UAACmC,IAAI,EAAE,EAAG;UAAAD,QAAA,eACZpB,OAAA,CAACX,UAAU;YAAAiC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAEV;IACA,OAAOlB,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEmB,GAAG,CAAEC,IAAI;MAAA,IAAAC,iBAAA,EAAAC,kBAAA,EAAAC,YAAA;MAAA,oBACxB9B,OAAA,CAACf,IAAI;QACH8C,SAAS,EAAC,cAAc;QAExBC,KAAK,eACHhC,OAAA;UACEiC,GAAG,EAAE,CAAAN,IAAI,aAAJA,IAAI,wBAAAC,iBAAA,GAAJD,IAAI,CAAEO,WAAW,cAAAN,iBAAA,uBAAjBA,iBAAA,CAAmBO,KAAK,KAAI9B,CAAC,CAAC,KAAK,CAAE;UAC1C+B,GAAG,EAAE,CAAAT,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEU,GAAG,KAAI;QAAkC;UAAAf,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrD,CACF;QACDa,OAAO,EAAEA,CAAA,KAAM;UACb3B,sBAAsB,CAACgB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEf,IAAI,CAAC;QACpC,CAAE;QAAAQ,QAAA,gBAEFpB,OAAA,CAACT,IAAI;UAAC4C,KAAK,EAAE,CAAAR,IAAI,aAAJA,IAAI,wBAAAE,kBAAA,GAAJF,IAAI,CAAEO,WAAW,cAAAL,kBAAA,uBAAjBA,kBAAA,CAAmBM,KAAK,KAAI9B,CAAC,CAAC,KAAK;QAAE;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACrDzB,OAAA;UAAK+B,SAAS,EAAC,SAAS;UAAAX,QAAA,eACtBpB,OAAA,CAACR,YAAY;YAAA8B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb,CAAC,EACLE,IAAI,aAAJA,IAAI,wBAAAG,YAAA,GAAJH,IAAI,CAAEY,MAAM,cAAAT,YAAA,uBAAZA,YAAA,CAAcJ,GAAG,CAAC,CAACc,EAAE,EAAEC,UAAU;UAAA,IAAAC,SAAA,EAAAC,UAAA;UAAA,oBAChC3C,OAAA;YAEE+B,SAAS,EAAES,EAAE,aAAFA,EAAE,eAAFA,EAAE,CAAEI,KAAK,GAAG,YAAY,GAAG,QAAS;YAAAxB,QAAA,gBAE/CpB,OAAA,CAACP,cAAc;cAAA6B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,KAAC,EAACe,EAAE,aAAFA,EAAE,wBAAAE,SAAA,GAAFF,EAAE,CAAEI,KAAK,cAAAF,SAAA,uBAATA,SAAA,CAAWG,KAAK,EACnC,GAAG,EACHL,EAAE,aAAFA,EAAE,wBAAAG,UAAA,GAAFH,EAAE,CAAEI,KAAK,cAAAD,UAAA,uBAATA,UAAA,CAAWG,cAAc;UAAA,GALrB,CAAAN,EAAE,aAAFA,EAAE,uBAAFA,EAAE,CAAEO,EAAE,KAAK,SAAQN,UAAW,EAAC;YAAAnB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAMhC,CAAC;QAAA,CACR,CAAC;MAAA,GAxBGE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoB,EAAE;QAAAzB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAyBT,CAAC;IAAA,CACR,CAAC;EACJ,CAAC;EACD,oBACEzB,OAAA,CAAAE,SAAA;IAAAkB,QAAA,GACGZ,OAAO,iBACNR,OAAA;MAAK+B,SAAS,EAAC,QAAQ;MAAAX,QAAA,eACrBpB,OAAA,CAACZ,IAAI;QAAAkC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACN,eACDzB,OAAA;MAAK+B,SAAS,EAAC,0BAA0B;MAAAX,QAAA,EAAEJ,UAAU,CAAC;IAAC;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,EAC7D,CAAC,CAACnB,WAAW,CAAC0C,GAAG,CAAC,MAAM,CAAC,iBACxBhD,OAAA,CAACF,YAAY;MACXc,IAAI,EAAEN,WAAW,CAAC0C,GAAG,CAAC,MAAM,CAAE;MAC9BC,QAAQ,EAAEnC;IAAwB;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnC,CACF;EAAA,eACD,CAAC;AAEP,CAAC;AAACrB,EAAA,CA1EID,WAAW;EAAA,QACDT,cAAc,EACRG,cAAc,EAEJD,WAAW;AAAA;AAAAsD,EAAA,GAJrC/C,WAAW;AA4EjB,eAAeA,WAAW;AAAC,IAAA+C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}