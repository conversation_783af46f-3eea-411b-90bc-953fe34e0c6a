{"ast": null, "code": "var _jsxFileName = \"C:\\\\OSPanel\\\\home\\\\admin.ticketflow.chat\\\\src\\\\components\\\\chart-widget.js\",\n  _s = $RefreshSig$();\nimport React, { useRef, useEffect } from 'react';\nimport PropTypes from 'prop-types';\nimport { Card } from 'antd';\nimport Apex<PERSON>hart from 'react-apexcharts';\nimport ReactResizeDetector from 'react-resize-detector';\nimport { apexAreaChartDefaultOption, apexBarChartDefaultOption, apexLineChartDefaultOption, apexPieChartDefaultOption } from '../constants/ChartConstant';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst DIR_RTL = 'rtl';\nconst titleStyle = {\n  position: 'absolute',\n  zIndex: '1'\n};\nconst extraStyle = {\n  position: 'absolute',\n  zIndex: '1',\n  right: '0',\n  top: '-2px'\n};\nconst getChartTypeDefaultOption = type => {\n  switch (type) {\n    case 'line':\n      return apexLineChartDefaultOption;\n    case 'bar':\n      return apexBarChartDefaultOption;\n    case 'area':\n      return apexAreaChartDefaultOption;\n    case 'pie':\n      return apexPieChartDefaultOption;\n    default:\n      return apexLineChartDefaultOption;\n  }\n};\nconst ChartWidget = ({\n  title,\n  series,\n  width,\n  height,\n  xAxis,\n  customOptions,\n  card,\n  type,\n  extra,\n  direction,\n  bodyClass\n}) => {\n  _s();\n  let options = JSON.parse(JSON.stringify(getChartTypeDefaultOption(type)));\n  const isMobile = window.innerWidth < 768;\n  const setLegendOffset = () => {\n    if (chartRef.current) {\n      var _extraRef$current;\n      const lengend = chartRef.current.querySelectorAll('div.apexcharts-legend')[0];\n      lengend.style.marginRight = `${isMobile ? 0 : (_extraRef$current = extraRef.current) === null || _extraRef$current === void 0 ? void 0 : _extraRef$current.offsetWidth}px`;\n      if (direction === DIR_RTL) {\n        lengend.style.right = 'auto';\n        lengend.style.left = '0';\n      }\n      if (isMobile) {\n        lengend.style.position = 'relative';\n        lengend.style.top = 0;\n        lengend.style.justifyContent = 'start';\n        lengend.style.padding = 0;\n      }\n    }\n  };\n  useEffect(() => {\n    setLegendOffset();\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n  const extraRef = useRef(null);\n  const chartRef = useRef();\n  options.xaxis = {\n    categories: xAxis\n  };\n  if (customOptions) {\n    options = {\n      ...options,\n      ...customOptions\n    };\n  }\n  const onResize = () => {\n    setTimeout(() => {\n      setLegendOffset();\n    }, 600);\n  };\n  const renderChart = () => /*#__PURE__*/_jsxDEV(ReactResizeDetector, {\n    handleWidth: true,\n    handleHeight: true,\n    onResize: onResize,\n    targetRef: chartRef,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      style: direction === DIR_RTL ? {\n        direction: 'ltr'\n      } : {},\n      className: \"chartRef\",\n      ref: chartRef,\n      children: /*#__PURE__*/_jsxDEV(ApexChart, {\n        options: options,\n        type: type,\n        series: series,\n        width: width,\n        height: height\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 112,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 107,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 101,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: card ? /*#__PURE__*/_jsxDEV(Card, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `position-relative ${bodyClass}`,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: !isMobile ? titleStyle : {},\n          children: title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 128,\n          columnNumber: 14\n        }, this) && /*#__PURE__*/_jsxDEV(\"h4\", {\n          className: \"font-weight-bold\",\n          style: !isMobile ? titleStyle : {},\n          children: title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 15\n        }, this), extra && /*#__PURE__*/_jsxDEV(\"div\", {\n          ref: extraRef,\n          style: !isMobile ? extraStyle : {},\n          children: extra\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 137,\n          columnNumber: 15\n        }, this), renderChart()]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 127,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 126,\n      columnNumber: 9\n    }, this) : renderChart()\n  }, void 0, false);\n};\n_s(ChartWidget, \"yuOowm7QWeqbFUnrB2gsEuXomek=\");\n_c = ChartWidget;\nChartWidget.propTypes = {\n  title: PropTypes.oneOfType([PropTypes.string, PropTypes.element]),\n  series: PropTypes.array.isRequired,\n  xAxis: PropTypes.array,\n  customOptions: PropTypes.object,\n  width: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),\n  height: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),\n  card: PropTypes.bool,\n  type: PropTypes.string,\n  extra: PropTypes.element,\n  bodyClass: PropTypes.string\n};\nChartWidget.defaultProps = {\n  series: [],\n  height: 300,\n  width: '100%',\n  card: true,\n  type: 'line'\n};\nexport default ChartWidget;\nvar _c;\n$RefreshReg$(_c, \"ChartWidget\");", "map": {"version": 3, "names": ["React", "useRef", "useEffect", "PropTypes", "Card", "ApexChart", "ReactResizeDetector", "apexAreaChartDefaultOption", "apexBarChartDefaultOption", "apexLineChartDefaultOption", "apexPieChartDefaultOption", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "DIR_RTL", "titleStyle", "position", "zIndex", "extraStyle", "right", "top", "getChartTypeDefaultOption", "type", "ChartWidget", "title", "series", "width", "height", "xAxis", "customOptions", "card", "extra", "direction", "bodyClass", "_s", "options", "JSON", "parse", "stringify", "isMobile", "window", "innerWidth", "setLegendOffset", "chartRef", "current", "_extraRef$current", "lengend", "querySelectorAll", "style", "marginRight", "extraRef", "offsetWidth", "left", "justifyContent", "padding", "xaxis", "categories", "onResize", "setTimeout", "<PERSON><PERSON><PERSON>", "handleWidth", "handleHeight", "targetRef", "children", "className", "ref", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "propTypes", "oneOfType", "string", "element", "array", "isRequired", "object", "number", "bool", "defaultProps", "$RefreshReg$"], "sources": ["C:/OSPanel/home/<USER>/src/components/chart-widget.js"], "sourcesContent": ["import React, { useRef, useEffect } from 'react';\nimport PropTypes from 'prop-types';\nimport { Card } from 'antd';\nimport Apex<PERSON>hart from 'react-apexcharts';\nimport ReactResizeDetector from 'react-resize-detector';\nimport {\n  apexAreaChartDefaultOption,\n  apexBarChartDefaultOption,\n  apexLineChartDefaultOption,\n  apexPieChartDefaultOption,\n} from '../constants/ChartConstant';\n\nconst DIR_RTL = 'rtl';\n\nconst titleStyle = {\n  position: 'absolute',\n  zIndex: '1',\n};\n\nconst extraStyle = {\n  position: 'absolute',\n  zIndex: '1',\n  right: '0',\n  top: '-2px',\n};\n\nconst getChartTypeDefaultOption = (type) => {\n  switch (type) {\n    case 'line':\n      return apexLineChartDefaultOption;\n    case 'bar':\n      return apexBarChartDefaultOption;\n    case 'area':\n      return apexAreaChartDefaultOption;\n    case 'pie':\n      return apexPieChartDefaultOption;\n    default:\n      return apexLineChartDefaultOption;\n  }\n};\n\nconst ChartWidget = ({\n  title,\n  series,\n  width,\n  height,\n  xAxis,\n  customOptions,\n  card,\n  type,\n  extra,\n  direction,\n  bodyClass,\n}) => {\n  let options = JSON.parse(JSON.stringify(getChartTypeDefaultOption(type)));\n\n  const isMobile = window.innerWidth < 768;\n  const setLegendOffset = () => {\n    if (chartRef.current) {\n      const lengend = chartRef.current.querySelectorAll(\n        'div.apexcharts-legend'\n      )[0];\n      lengend.style.marginRight = `${\n        isMobile ? 0 : extraRef.current?.offsetWidth\n      }px`;\n      if (direction === DIR_RTL) {\n        lengend.style.right = 'auto';\n        lengend.style.left = '0';\n      }\n      if (isMobile) {\n        lengend.style.position = 'relative';\n        lengend.style.top = 0;\n        lengend.style.justifyContent = 'start';\n        lengend.style.padding = 0;\n      }\n    }\n  };\n\n  useEffect(() => {\n    setLegendOffset();\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n\n  const extraRef = useRef(null);\n  const chartRef = useRef();\n\n  options.xaxis = {\n    categories: xAxis,\n  };\n  if (customOptions) {\n    options = { ...options, ...customOptions };\n  }\n\n  const onResize = () => {\n    setTimeout(() => {\n      setLegendOffset();\n    }, 600);\n  };\n\n  const renderChart = () => (\n    <ReactResizeDetector\n      handleWidth\n      handleHeight\n      onResize={onResize}\n      targetRef={chartRef}\n    >\n      <div\n        style={direction === DIR_RTL ? { direction: 'ltr' } : {}}\n        className='chartRef'\n        ref={chartRef}\n      >\n        <ApexChart\n          options={options}\n          type={type}\n          series={series}\n          width={width}\n          height={height}\n        />\n      </div>\n    </ReactResizeDetector>\n  );\n\n  return (\n    <>\n      {card ? (\n        <Card>\n          <div className={`position-relative ${bodyClass}`}>\n            {<div style={!isMobile ? titleStyle : {}}>{title}</div> && (\n              <h4\n                className='font-weight-bold'\n                style={!isMobile ? titleStyle : {}}\n              >\n                {title}\n              </h4>\n            )}\n            {extra && (\n              <div ref={extraRef} style={!isMobile ? extraStyle : {}}>\n                {extra}\n              </div>\n            )}\n            {renderChart()}\n          </div>\n        </Card>\n      ) : (\n        renderChart()\n      )}\n    </>\n  );\n};\n\nChartWidget.propTypes = {\n  title: PropTypes.oneOfType([PropTypes.string, PropTypes.element]),\n  series: PropTypes.array.isRequired,\n  xAxis: PropTypes.array,\n  customOptions: PropTypes.object,\n  width: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),\n  height: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),\n  card: PropTypes.bool,\n  type: PropTypes.string,\n  extra: PropTypes.element,\n  bodyClass: PropTypes.string,\n};\n\nChartWidget.defaultProps = {\n  series: [],\n  height: 300,\n  width: '100%',\n  card: true,\n  type: 'line',\n};\n\nexport default ChartWidget;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAChD,OAAOC,SAAS,MAAM,YAAY;AAClC,SAASC,IAAI,QAAQ,MAAM;AAC3B,OAAOC,SAAS,MAAM,kBAAkB;AACxC,OAAOC,mBAAmB,MAAM,uBAAuB;AACvD,SACEC,0BAA0B,EAC1BC,yBAAyB,EACzBC,0BAA0B,EAC1BC,yBAAyB,QACpB,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEpC,MAAMC,OAAO,GAAG,KAAK;AAErB,MAAMC,UAAU,GAAG;EACjBC,QAAQ,EAAE,UAAU;EACpBC,MAAM,EAAE;AACV,CAAC;AAED,MAAMC,UAAU,GAAG;EACjBF,QAAQ,EAAE,UAAU;EACpBC,MAAM,EAAE,GAAG;EACXE,KAAK,EAAE,GAAG;EACVC,GAAG,EAAE;AACP,CAAC;AAED,MAAMC,yBAAyB,GAAIC,IAAI,IAAK;EAC1C,QAAQA,IAAI;IACV,KAAK,MAAM;MACT,OAAOd,0BAA0B;IACnC,KAAK,KAAK;MACR,OAAOD,yBAAyB;IAClC,KAAK,MAAM;MACT,OAAOD,0BAA0B;IACnC,KAAK,KAAK;MACR,OAAOG,yBAAyB;IAClC;MACE,OAAOD,0BAA0B;EACrC;AACF,CAAC;AAED,MAAMe,WAAW,GAAGA,CAAC;EACnBC,KAAK;EACLC,MAAM;EACNC,KAAK;EACLC,MAAM;EACNC,KAAK;EACLC,aAAa;EACbC,IAAI;EACJR,IAAI;EACJS,KAAK;EACLC,SAAS;EACTC;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ,IAAIC,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,SAAS,CAACjB,yBAAyB,CAACC,IAAI,CAAC,CAAC,CAAC;EAEzE,MAAMiB,QAAQ,GAAGC,MAAM,CAACC,UAAU,GAAG,GAAG;EACxC,MAAMC,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAIC,QAAQ,CAACC,OAAO,EAAE;MAAA,IAAAC,iBAAA;MACpB,MAAMC,OAAO,GAAGH,QAAQ,CAACC,OAAO,CAACG,gBAAgB,CAC/C,uBACF,CAAC,CAAC,CAAC,CAAC;MACJD,OAAO,CAACE,KAAK,CAACC,WAAW,GAAI,GAC3BV,QAAQ,GAAG,CAAC,IAAAM,iBAAA,GAAGK,QAAQ,CAACN,OAAO,cAAAC,iBAAA,uBAAhBA,iBAAA,CAAkBM,WAClC,IAAG;MACJ,IAAInB,SAAS,KAAKlB,OAAO,EAAE;QACzBgC,OAAO,CAACE,KAAK,CAAC7B,KAAK,GAAG,MAAM;QAC5B2B,OAAO,CAACE,KAAK,CAACI,IAAI,GAAG,GAAG;MAC1B;MACA,IAAIb,QAAQ,EAAE;QACZO,OAAO,CAACE,KAAK,CAAChC,QAAQ,GAAG,UAAU;QACnC8B,OAAO,CAACE,KAAK,CAAC5B,GAAG,GAAG,CAAC;QACrB0B,OAAO,CAACE,KAAK,CAACK,cAAc,GAAG,OAAO;QACtCP,OAAO,CAACE,KAAK,CAACM,OAAO,GAAG,CAAC;MAC3B;IACF;EACF,CAAC;EAEDrD,SAAS,CAAC,MAAM;IACdyC,eAAe,CAAC,CAAC;IACjB;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMQ,QAAQ,GAAGlD,MAAM,CAAC,IAAI,CAAC;EAC7B,MAAM2C,QAAQ,GAAG3C,MAAM,CAAC,CAAC;EAEzBmC,OAAO,CAACoB,KAAK,GAAG;IACdC,UAAU,EAAE5B;EACd,CAAC;EACD,IAAIC,aAAa,EAAE;IACjBM,OAAO,GAAG;MAAE,GAAGA,OAAO;MAAE,GAAGN;IAAc,CAAC;EAC5C;EAEA,MAAM4B,QAAQ,GAAGA,CAAA,KAAM;IACrBC,UAAU,CAAC,MAAM;MACfhB,eAAe,CAAC,CAAC;IACnB,CAAC,EAAE,GAAG,CAAC;EACT,CAAC;EAED,MAAMiB,WAAW,GAAGA,CAAA,kBAClBhD,OAAA,CAACN,mBAAmB;IAClBuD,WAAW;IACXC,YAAY;IACZJ,QAAQ,EAAEA,QAAS;IACnBK,SAAS,EAAEnB,QAAS;IAAAoB,QAAA,eAEpBpD,OAAA;MACEqC,KAAK,EAAEhB,SAAS,KAAKlB,OAAO,GAAG;QAAEkB,SAAS,EAAE;MAAM,CAAC,GAAG,CAAC,CAAE;MACzDgC,SAAS,EAAC,UAAU;MACpBC,GAAG,EAAEtB,QAAS;MAAAoB,QAAA,eAEdpD,OAAA,CAACP,SAAS;QACR+B,OAAO,EAAEA,OAAQ;QACjBb,IAAI,EAAEA,IAAK;QACXG,MAAM,EAAEA,MAAO;QACfC,KAAK,EAAEA,KAAM;QACbC,MAAM,EAAEA;MAAO;QAAAuC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACa,CACtB;EAED,oBACE1D,OAAA,CAAAE,SAAA;IAAAkD,QAAA,EACGjC,IAAI,gBACHnB,OAAA,CAACR,IAAI;MAAA4D,QAAA,eACHpD,OAAA;QAAKqD,SAAS,EAAG,qBAAoB/B,SAAU,EAAE;QAAA8B,QAAA,GAC9C,aAAApD,OAAA;UAAKqC,KAAK,EAAE,CAACT,QAAQ,GAAGxB,UAAU,GAAG,CAAC,CAAE;UAAAgD,QAAA,EAAEvC;QAAK;UAAA0C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,iBACrD1D,OAAA;UACEqD,SAAS,EAAC,kBAAkB;UAC5BhB,KAAK,EAAE,CAACT,QAAQ,GAAGxB,UAAU,GAAG,CAAC,CAAE;UAAAgD,QAAA,EAElCvC;QAAK;UAAA0C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CACL,EACAtC,KAAK,iBACJpB,OAAA;UAAKsD,GAAG,EAAEf,QAAS;UAACF,KAAK,EAAE,CAACT,QAAQ,GAAGrB,UAAU,GAAG,CAAC,CAAE;UAAA6C,QAAA,EACpDhC;QAAK;UAAAmC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,EACAV,WAAW,CAAC,CAAC;MAAA;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACX;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,GAEPV,WAAW,CAAC;EACb,gBACD,CAAC;AAEP,CAAC;AAACzB,EAAA,CA3GIX,WAAW;AAAA+C,EAAA,GAAX/C,WAAW;AA6GjBA,WAAW,CAACgD,SAAS,GAAG;EACtB/C,KAAK,EAAEtB,SAAS,CAACsE,SAAS,CAAC,CAACtE,SAAS,CAACuE,MAAM,EAAEvE,SAAS,CAACwE,OAAO,CAAC,CAAC;EACjEjD,MAAM,EAAEvB,SAAS,CAACyE,KAAK,CAACC,UAAU;EAClChD,KAAK,EAAE1B,SAAS,CAACyE,KAAK;EACtB9C,aAAa,EAAE3B,SAAS,CAAC2E,MAAM;EAC/BnD,KAAK,EAAExB,SAAS,CAACsE,SAAS,CAAC,CAACtE,SAAS,CAACuE,MAAM,EAAEvE,SAAS,CAAC4E,MAAM,CAAC,CAAC;EAChEnD,MAAM,EAAEzB,SAAS,CAACsE,SAAS,CAAC,CAACtE,SAAS,CAACuE,MAAM,EAAEvE,SAAS,CAAC4E,MAAM,CAAC,CAAC;EACjEhD,IAAI,EAAE5B,SAAS,CAAC6E,IAAI;EACpBzD,IAAI,EAAEpB,SAAS,CAACuE,MAAM;EACtB1C,KAAK,EAAE7B,SAAS,CAACwE,OAAO;EACxBzC,SAAS,EAAE/B,SAAS,CAACuE;AACvB,CAAC;AAEDlD,WAAW,CAACyD,YAAY,GAAG;EACzBvD,MAAM,EAAE,EAAE;EACVE,MAAM,EAAE,GAAG;EACXD,KAAK,EAAE,MAAM;EACbI,IAAI,EAAE,IAAI;EACVR,IAAI,EAAE;AACR,CAAC;AAED,eAAeC,WAAW;AAAC,IAAA+C,EAAA;AAAAW,YAAA,CAAAX,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}