import React from 'react';
import { Card, Alert, Button } from 'antd';
import { ReloadOutlined } from '@ant-design/icons';

class MapErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null, errorInfo: null };
  }

  static getDerivedStateFromError() {
    // Update state so the next render will show the fallback UI
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    // Log the error for debugging
    console.error('MapErrorBoundary caught an error:', error, errorInfo);
    this.setState({
      error: error,
      errorInfo: errorInfo
    });
  }

  handleReload = () => {
    // Reset the error boundary state
    this.setState({ hasError: false, error: null, errorInfo: null });
    // Optionally reload the page
    window.location.reload();
  };

  render() {
    if (this.state.hasError) {
      // Fallback UI when map fails to load
      return (
        <Card 
          title="Map Loading Error" 
          style={{ height: '73vh', display: 'flex', alignItems: 'center', justifyContent: 'center' }}
        >
          <div style={{ textAlign: 'center', padding: '20px' }}>
            <Alert
              message="Map Failed to Load"
              description="There was an error loading the map. This might be due to invalid coordinates or network issues."
              type="error"
              showIcon
              style={{ marginBottom: '20px' }}
            />
            <Button 
              type="primary" 
              icon={<ReloadOutlined />} 
              onClick={this.handleReload}
            >
              Reload Map
            </Button>
            {process.env.NODE_ENV === 'development' && (
              <details style={{ marginTop: '20px', textAlign: 'left' }}>
                <summary>Error Details (Development Only)</summary>
                <pre style={{ fontSize: '12px', color: '#666' }}>
                  {this.state.error && this.state.error.toString()}
                  <br />
                  {this.state.errorInfo.componentStack}
                </pre>
              </details>
            )}
          </div>
        </Card>
      );
    }

    return this.props.children;
  }
}

export default MapErrorBoundary;
