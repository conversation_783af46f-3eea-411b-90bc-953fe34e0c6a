import axios from 'axios';
import { notification } from 'antd';
import { api_url } from '../configs/app-global';
import { store } from '../redux/store';
import { clearUser } from '../redux/slices/auth';
import i18n from '../configs/i18next';
import { toast } from 'react-toastify';

const service = axios.create({
  baseURL: api_url,
  timeout: 30000, // Increased to 30 seconds for better reliability
});

// Config
const TOKEN_PAYLOAD_KEY = 'authorization';
const AUTH_TOKEN = 'token';
const AUTH_TOKEN_TYPE = 'Bearer';

// API Request interceptor
service.interceptors.request.use(
  (config) => {
    const access_token = localStorage.getItem(AUTH_TOKEN);

    if (access_token) {
      config.headers[TOKEN_PAYLOAD_KEY] = AUTH_TOKEN_TYPE + ' ' + access_token;
    }
    if (config.method === 'get') {
      config.params = { lang: i18n.language, ...config.params };
    }

    return config;
  },
  (error) => {
    // Do something with request error here
    notification.error({
      message: 'Error',
    });
    Promise.reject(error);
  }
);

// API respone interceptor
service.interceptors.response.use(
  (response) => {
    return response.data;
  },
  (error) => {
    let notificationParam = {
      message: i18n.t(error.response?.data?.message || 'network.error'),
    };

    // Check if error.response exists before accessing its properties
    if (error.response) {
      // Remove token and redirect
      if (error.response.status === 403 || error.response.status === 401) {
        localStorage.removeItem(AUTH_TOKEN);
        store.dispatch(clearUser());
      }

      if (error.response.status === 508) {
        notificationParam.message = error.response.data?.message || 'server.error';
      }

      if (error.response.status === 500) {
        notificationParam.message = error.response.data?.message || 'internal.server.error';
      }

      if(error.response.data?.params) {
        if(Object.values(error.response.data.params)[0]){
          notificationParam.message = Object.values(error.response.data.params)[0].at(0)
        }
      }

      toast.error(notificationParam.message, {
        toastId: error.response.status,
      });
    } else {
      // Handle network errors, timeout errors, or other errors without response
      if (error.code === 'ECONNABORTED' || error.message.includes('timeout')) {
        notificationParam.message = 'Request timeout. Please try again.';
        console.warn('Request timeout occurred:', error.config?.url);
      } else {
        notificationParam.message = error.message || 'network.connection.error';
      }

      toast.error(notificationParam.message, {
        toastId: error.code === 'ECONNABORTED' ? 'timeout-error' : 'network-error',
      });
    }

    return Promise.reject(error);
  }
);

export default service;
