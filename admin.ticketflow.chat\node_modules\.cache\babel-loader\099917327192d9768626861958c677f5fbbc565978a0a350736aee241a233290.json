{"ast": null, "code": "var _jsxFileName = \"C:\\\\OSPanel\\\\home\\\\admin.ticketflow.chat\\\\src\\\\components\\\\lang-modal.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport { Button, Form, Modal, Select } from 'antd';\nimport { useTranslation } from 'react-i18next';\nimport i18n from '../configs/i18next';\nimport informationService from '../services/rest/information';\nimport { shallowEqual, useDispatch, useSelector } from 'react-redux';\nimport { directionChange } from '../redux/slices/theme';\nimport { setLangugages } from '../redux/slices/formLang';\nimport languagesService from '../services/languages';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport default function LangModal({\n  visible,\n  handleCancel\n}) {\n  _s();\n  const {\n    t\n  } = useTranslation();\n  const [form] = Form.useForm();\n  const [loading, setLoading] = useState(false);\n  const {\n    languages\n  } = useSelector(state => state.formLang, shallowEqual);\n  const dispatch = useDispatch();\n  const isMountedRef = useRef(true);\n  const onFinish = values => {\n    const {\n      lang\n    } = values;\n    const locale = languages.find(item => item.locale === lang);\n    const direction = locale.backward ? 'rtl' : 'ltr';\n    if (isMountedRef.current) {\n      setLoading(true);\n    }\n    informationService.translations(values).then(({\n      data\n    }) => {\n      if (isMountedRef.current) {\n        i18n.addResourceBundle(lang, 'translation', data);\n        handleCancel();\n        i18n.changeLanguage(lang);\n        dispatch(directionChange(direction));\n      }\n    }).finally(() => {\n      if (isMountedRef.current) {\n        setLoading(false);\n      }\n    });\n  };\n  const fetchLanguages = () => {\n    languagesService.getAllActive().then(({\n      data\n    }) => {\n      if (isMountedRef.current) {\n        dispatch(setLangugages(data));\n      }\n    });\n  };\n  useEffect(() => {\n    fetchLanguages();\n\n    // Cleanup function to prevent memory leaks\n    return () => {\n      isMountedRef.current = false;\n    };\n  }, []);\n  return /*#__PURE__*/_jsxDEV(Modal, {\n    title: t('change.language'),\n    visible: visible,\n    onCancel: handleCancel,\n    footer: [/*#__PURE__*/_jsxDEV(Button, {\n      type: \"primary\",\n      onClick: () => form.submit(),\n      loading: loading,\n      children: t('save')\n    }, 'ok-button', false, {\n      fileName: _jsxFileName,\n      lineNumber: 66,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Button, {\n      onClick: handleCancel,\n      children: t('cancel')\n    }, 'cancel-button', false, {\n      fileName: _jsxFileName,\n      lineNumber: 74,\n      columnNumber: 9\n    }, this)],\n    children: /*#__PURE__*/_jsxDEV(Form, {\n      layout: \"vertical\",\n      name: \"lang-form\",\n      form: form,\n      onFinish: onFinish,\n      initialValues: {\n        lang: i18n.language\n      },\n      children: /*#__PURE__*/_jsxDEV(Form.Item, {\n        label: t('language'),\n        name: \"lang\",\n        children: /*#__PURE__*/_jsxDEV(Select, {\n          children: languages.map((item, idx) => /*#__PURE__*/_jsxDEV(Select.Option, {\n            value: item.locale,\n            children: item.title\n          }, item.locale + idx, false, {\n            fileName: _jsxFileName,\n            lineNumber: 89,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 87,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 86,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 79,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 61,\n    columnNumber: 5\n  }, this);\n}\n_s(LangModal, \"lm2uosB2SBA02DLKgTFVJxdHjtA=\", false, function () {\n  return [useTranslation, Form.useForm, useSelector, useDispatch];\n});\n_c = LangModal;\nvar _c;\n$RefreshReg$(_c, \"LangModal\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "<PERSON><PERSON>", "Form", "Modal", "Select", "useTranslation", "i18n", "informationService", "shallowEqual", "useDispatch", "useSelector", "directionChange", "setLangugages", "languagesService", "jsxDEV", "_jsxDEV", "LangModal", "visible", "handleCancel", "_s", "t", "form", "useForm", "loading", "setLoading", "languages", "state", "formLang", "dispatch", "isMountedRef", "onFinish", "values", "lang", "locale", "find", "item", "direction", "backward", "current", "translations", "then", "data", "addResourceBundle", "changeLanguage", "finally", "fetchLanguages", "getAllActive", "title", "onCancel", "footer", "type", "onClick", "submit", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "layout", "name", "initialValues", "language", "<PERSON><PERSON>", "label", "map", "idx", "Option", "value", "_c", "$RefreshReg$"], "sources": ["C:/OSPanel/home/<USER>/src/components/lang-modal.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport { Button, Form, Modal, Select } from 'antd';\nimport { useTranslation } from 'react-i18next';\nimport i18n from '../configs/i18next';\nimport informationService from '../services/rest/information';\nimport { shallowEqual, useDispatch, useSelector } from 'react-redux';\nimport { directionChange } from '../redux/slices/theme';\nimport { setLangugages } from '../redux/slices/formLang';\nimport languagesService from '../services/languages';\n\nexport default function LangModal({ visible, handleCancel }) {\n  const { t } = useTranslation();\n  const [form] = Form.useForm();\n  const [loading, setLoading] = useState(false);\n  const { languages } = useSelector((state) => state.formLang, shallowEqual);\n  const dispatch = useDispatch();\n  const isMountedRef = useRef(true);\n\n  const onFinish = (values) => {\n    const { lang } = values;\n    const locale = languages.find((item) => item.locale === lang);\n    const direction = locale.backward ? 'rtl' : 'ltr';\n    if (isMountedRef.current) {\n      setLoading(true);\n    }\n    informationService\n      .translations(values)\n      .then(({ data }) => {\n        if (isMountedRef.current) {\n          i18n.addResourceBundle(lang, 'translation', data);\n          handleCancel();\n          i18n.changeLanguage(lang);\n          dispatch(directionChange(direction));\n        }\n      })\n      .finally(() => {\n        if (isMountedRef.current) {\n          setLoading(false);\n        }\n      });\n  };\n\n  const fetchLanguages = () => {\n    languagesService.getAllActive().then(({ data }) => {\n      if (isMountedRef.current) {\n        dispatch(setLangugages(data));\n      }\n    });\n  };\n\n  useEffect(() => {\n    fetchLanguages();\n\n    // Cleanup function to prevent memory leaks\n    return () => {\n      isMountedRef.current = false;\n    };\n  }, []);\n\n  return (\n    <Modal\n      title={t('change.language')}\n      visible={visible}\n      onCancel={handleCancel}\n      footer={[\n        <Button\n          key='ok-button'\n          type='primary'\n          onClick={() => form.submit()}\n          loading={loading}\n        >\n          {t('save')}\n        </Button>,\n        <Button key='cancel-button' onClick={handleCancel}>\n          {t('cancel')}\n        </Button>,\n      ]}\n    >\n      <Form\n        layout='vertical'\n        name='lang-form'\n        form={form}\n        onFinish={onFinish}\n        initialValues={{ lang: i18n.language }}\n      >\n        <Form.Item label={t('language')} name='lang'>\n          <Select>\n            {languages.map((item, idx) => (\n              <Select.Option key={item.locale + idx} value={item.locale}>\n                {item.title}\n              </Select.Option>\n            ))}\n          </Select>\n        </Form.Item>\n      </Form>\n    </Modal>\n  );\n}\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,MAAM,EAAEC,IAAI,EAAEC,KAAK,EAAEC,MAAM,QAAQ,MAAM;AAClD,SAASC,cAAc,QAAQ,eAAe;AAC9C,OAAOC,IAAI,MAAM,oBAAoB;AACrC,OAAOC,kBAAkB,MAAM,8BAA8B;AAC7D,SAASC,YAAY,EAAEC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACpE,SAASC,eAAe,QAAQ,uBAAuB;AACvD,SAASC,aAAa,QAAQ,0BAA0B;AACxD,OAAOC,gBAAgB,MAAM,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErD,eAAe,SAASC,SAASA,CAAC;EAAEC,OAAO;EAAEC;AAAa,CAAC,EAAE;EAAAC,EAAA;EAC3D,MAAM;IAAEC;EAAE,CAAC,GAAGf,cAAc,CAAC,CAAC;EAC9B,MAAM,CAACgB,IAAI,CAAC,GAAGnB,IAAI,CAACoB,OAAO,CAAC,CAAC;EAC7B,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM;IAAE2B;EAAU,CAAC,GAAGf,WAAW,CAAEgB,KAAK,IAAKA,KAAK,CAACC,QAAQ,EAAEnB,YAAY,CAAC;EAC1E,MAAMoB,QAAQ,GAAGnB,WAAW,CAAC,CAAC;EAC9B,MAAMoB,YAAY,GAAG7B,MAAM,CAAC,IAAI,CAAC;EAEjC,MAAM8B,QAAQ,GAAIC,MAAM,IAAK;IAC3B,MAAM;MAAEC;IAAK,CAAC,GAAGD,MAAM;IACvB,MAAME,MAAM,GAAGR,SAAS,CAACS,IAAI,CAAEC,IAAI,IAAKA,IAAI,CAACF,MAAM,KAAKD,IAAI,CAAC;IAC7D,MAAMI,SAAS,GAAGH,MAAM,CAACI,QAAQ,GAAG,KAAK,GAAG,KAAK;IACjD,IAAIR,YAAY,CAACS,OAAO,EAAE;MACxBd,UAAU,CAAC,IAAI,CAAC;IAClB;IACAjB,kBAAkB,CACfgC,YAAY,CAACR,MAAM,CAAC,CACpBS,IAAI,CAAC,CAAC;MAAEC;IAAK,CAAC,KAAK;MAClB,IAAIZ,YAAY,CAACS,OAAO,EAAE;QACxBhC,IAAI,CAACoC,iBAAiB,CAACV,IAAI,EAAE,aAAa,EAAES,IAAI,CAAC;QACjDvB,YAAY,CAAC,CAAC;QACdZ,IAAI,CAACqC,cAAc,CAACX,IAAI,CAAC;QACzBJ,QAAQ,CAACjB,eAAe,CAACyB,SAAS,CAAC,CAAC;MACtC;IACF,CAAC,CAAC,CACDQ,OAAO,CAAC,MAAM;MACb,IAAIf,YAAY,CAACS,OAAO,EAAE;QACxBd,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC,CAAC;EACN,CAAC;EAED,MAAMqB,cAAc,GAAGA,CAAA,KAAM;IAC3BhC,gBAAgB,CAACiC,YAAY,CAAC,CAAC,CAACN,IAAI,CAAC,CAAC;MAAEC;IAAK,CAAC,KAAK;MACjD,IAAIZ,YAAY,CAACS,OAAO,EAAE;QACxBV,QAAQ,CAAChB,aAAa,CAAC6B,IAAI,CAAC,CAAC;MAC/B;IACF,CAAC,CAAC;EACJ,CAAC;EAED1C,SAAS,CAAC,MAAM;IACd8C,cAAc,CAAC,CAAC;;IAEhB;IACA,OAAO,MAAM;MACXhB,YAAY,CAACS,OAAO,GAAG,KAAK;IAC9B,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,oBACEvB,OAAA,CAACZ,KAAK;IACJ4C,KAAK,EAAE3B,CAAC,CAAC,iBAAiB,CAAE;IAC5BH,OAAO,EAAEA,OAAQ;IACjB+B,QAAQ,EAAE9B,YAAa;IACvB+B,MAAM,EAAE,cACNlC,OAAA,CAACd,MAAM;MAELiD,IAAI,EAAC,SAAS;MACdC,OAAO,EAAEA,CAAA,KAAM9B,IAAI,CAAC+B,MAAM,CAAC,CAAE;MAC7B7B,OAAO,EAAEA,OAAQ;MAAA8B,QAAA,EAEhBjC,CAAC,CAAC,MAAM;IAAC,GALN,WAAW;MAAAkC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAMT,CAAC,eACT1C,OAAA,CAACd,MAAM;MAAqBkD,OAAO,EAAEjC,YAAa;MAAAmC,QAAA,EAC/CjC,CAAC,CAAC,QAAQ;IAAC,GADF,eAAe;MAAAkC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEnB,CAAC,CACT;IAAAJ,QAAA,eAEFtC,OAAA,CAACb,IAAI;MACHwD,MAAM,EAAC,UAAU;MACjBC,IAAI,EAAC,WAAW;MAChBtC,IAAI,EAAEA,IAAK;MACXS,QAAQ,EAAEA,QAAS;MACnB8B,aAAa,EAAE;QAAE5B,IAAI,EAAE1B,IAAI,CAACuD;MAAS,CAAE;MAAAR,QAAA,eAEvCtC,OAAA,CAACb,IAAI,CAAC4D,IAAI;QAACC,KAAK,EAAE3C,CAAC,CAAC,UAAU,CAAE;QAACuC,IAAI,EAAC,MAAM;QAAAN,QAAA,eAC1CtC,OAAA,CAACX,MAAM;UAAAiD,QAAA,EACJ5B,SAAS,CAACuC,GAAG,CAAC,CAAC7B,IAAI,EAAE8B,GAAG,kBACvBlD,OAAA,CAACX,MAAM,CAAC8D,MAAM;YAAyBC,KAAK,EAAEhC,IAAI,CAACF,MAAO;YAAAoB,QAAA,EACvDlB,IAAI,CAACY;UAAK,GADOZ,IAAI,CAACF,MAAM,GAAGgC,GAAG;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEtB,CAChB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEZ;AAACtC,EAAA,CAvFuBH,SAAS;EAAA,QACjBX,cAAc,EACbH,IAAI,CAACoB,OAAO,EAELZ,WAAW,EAChBD,WAAW;AAAA;AAAA2D,EAAA,GALNpD,SAAS;AAAA,IAAAoD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}