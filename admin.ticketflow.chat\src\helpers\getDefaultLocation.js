import { LAT, LNG } from 'configs/app-global';

const isValidCoordinate = (value) => {
  const num = Number(value);
  return !isNaN(num) && isFinite(num);
};

export default function getDefaultLocation(settings) {
  // Default fallback coordinates
  const defaultCoords = {
    lat: 47.4143302506288,
    lng: 8.532059477976883,
  };

  // If no settings or location, return default
  if (!settings?.location || typeof settings.location !== 'string') {
    return defaultCoords;
  }

  // Try to parse the location string
  const location = settings.location.split(', ');
  const lat = location?.[0];
  const lng = location?.[1];

  // Validate both coordinates
  if (isValidCoordinate(lat) && isValidCoordinate(lng)) {
    return {
      lat: Number(lat),
      lng: Number(lng),
    };
  }

  // If validation fails, return default coordinates
  console.warn('Invalid location coordinates in settings:', settings.location, 'Using default coordinates');
  return defaultCoords;
}
