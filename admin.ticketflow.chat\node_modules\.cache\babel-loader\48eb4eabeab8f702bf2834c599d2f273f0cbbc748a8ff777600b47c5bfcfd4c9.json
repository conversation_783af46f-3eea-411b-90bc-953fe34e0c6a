{"ast": null, "code": "var _jsxFileName = \"C:\\\\OSPanel\\\\home\\\\admin.ticketflow.chat\\\\src\\\\components\\\\map-custom-marker.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport GoogleMapReact from 'google-map-react';\nimport { shallowEqual, useSelector } from 'react-redux';\nimport { MAP_API_KEY } from '../configs/app-global';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst isValidCoordinate = value => {\n  const num = Number(value);\n  return !isNaN(num) && isFinite(num);\n};\nexport default function MapCustomMarker({\n  center,\n  handleLoadMap,\n  children\n}) {\n  _s();\n  const {\n    google_map_key\n  } = useSelector(state => state.globalSettings.settings, shallowEqual);\n\n  // Validate center coordinates and provide fallback\n  const safeCenter = {\n    lat: isValidCoordinate(center === null || center === void 0 ? void 0 : center.lat) ? center.lat : 47.4143302506288,\n    lng: isValidCoordinate(center === null || center === void 0 ? void 0 : center.lng) ? center.lng : 8.532059477976883\n  };\n\n  // Log warning if invalid center was provided\n  if (!isValidCoordinate(center === null || center === void 0 ? void 0 : center.lat) || !isValidCoordinate(center === null || center === void 0 ? void 0 : center.lng)) {\n    console.warn('MapCustomMarker received invalid center coordinates:', center, 'Using fallback coordinates:', safeCenter);\n  }\n  const safeHandleLoadMap = (map, maps) => {\n    try {\n      if (handleLoadMap && typeof handleLoadMap === 'function') {\n        handleLoadMap(map, maps);\n      }\n    } catch (error) {\n      console.error('Error in handleLoadMap:', error);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(GoogleMapReact, {\n    bootstrapURLKeys: {\n      key: google_map_key || MAP_API_KEY\n    },\n    defaultZoom: 12,\n    center: safeCenter,\n    options: {\n      fullscreenControl: false\n    },\n    yesIWantToUseGoogleMapApiInternals: true,\n    onGoogleApiLoaded: ({\n      map,\n      maps\n    }) => safeHandleLoadMap(map, maps),\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 39,\n    columnNumber: 5\n  }, this);\n}\n_s(MapCustomMarker, \"VM8KymUk9dBIr0O/t0i3dfI2wKo=\", false, function () {\n  return [useSelector];\n});\n_c = MapCustomMarker;\nvar _c;\n$RefreshReg$(_c, \"MapCustomMarker\");", "map": {"version": 3, "names": ["React", "GoogleMapReact", "shallowEqual", "useSelector", "MAP_API_KEY", "jsxDEV", "_jsxDEV", "isValidCoordinate", "value", "num", "Number", "isNaN", "isFinite", "MapCustomMarker", "center", "handleLoadMap", "children", "_s", "google_map_key", "state", "globalSettings", "settings", "safeCenter", "lat", "lng", "console", "warn", "safeHandleLoadMap", "map", "maps", "error", "bootstrapURLKeys", "key", "defaultZoom", "options", "fullscreenControl", "yesIWantToUseGoogleMapApiInternals", "onGoogleApiLoaded", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/OSPanel/home/<USER>/src/components/map-custom-marker.js"], "sourcesContent": ["import React from 'react';\nimport GoogleMapReact from 'google-map-react';\nimport { shallowEqual, useSelector } from 'react-redux';\nimport { MAP_API_KEY } from '../configs/app-global';\n\nconst isValidCoordinate = (value) => {\n  const num = Number(value);\n  return !isNaN(num) && isFinite(num);\n};\n\nexport default function MapCustomMarker({ center, handleLoadMap, children }) {\n  const { google_map_key } = useSelector(\n    (state) => state.globalSettings.settings,\n    shallowEqual\n  );\n\n  // Validate center coordinates and provide fallback\n  const safeCenter = {\n    lat: isValidCoordinate(center?.lat) ? center.lat : 47.4143302506288,\n    lng: isValidCoordinate(center?.lng) ? center.lng : 8.532059477976883,\n  };\n\n  // Log warning if invalid center was provided\n  if (!isValidCoordinate(center?.lat) || !isValidCoordinate(center?.lng)) {\n    console.warn('MapCustomMarker received invalid center coordinates:', center, 'Using fallback coordinates:', safeCenter);\n  }\n\n  const safeHandleLoadMap = (map, maps) => {\n    try {\n      if (handleLoadMap && typeof handleLoadMap === 'function') {\n        handleLoadMap(map, maps);\n      }\n    } catch (error) {\n      console.error('Error in handleLoadMap:', error);\n    }\n  };\n\n  return (\n    <GoogleMapReact\n      bootstrapURLKeys={{\n        key: google_map_key || MAP_API_KEY,\n      }}\n      defaultZoom={12}\n      center={safeCenter}\n      options={{\n        fullscreenControl: false,\n      }}\n      yesIWantToUseGoogleMapApiInternals\n      onGoogleApiLoaded={({ map, maps }) => safeHandleLoadMap(map, maps)}\n    >\n      {children}\n    </GoogleMapReact>\n  );\n}\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,SAASC,YAAY,EAAEC,WAAW,QAAQ,aAAa;AACvD,SAASC,WAAW,QAAQ,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpD,MAAMC,iBAAiB,GAAIC,KAAK,IAAK;EACnC,MAAMC,GAAG,GAAGC,MAAM,CAACF,KAAK,CAAC;EACzB,OAAO,CAACG,KAAK,CAACF,GAAG,CAAC,IAAIG,QAAQ,CAACH,GAAG,CAAC;AACrC,CAAC;AAED,eAAe,SAASI,eAAeA,CAAC;EAAEC,MAAM;EAAEC,aAAa;EAAEC;AAAS,CAAC,EAAE;EAAAC,EAAA;EAC3E,MAAM;IAAEC;EAAe,CAAC,GAAGf,WAAW,CACnCgB,KAAK,IAAKA,KAAK,CAACC,cAAc,CAACC,QAAQ,EACxCnB,YACF,CAAC;;EAED;EACA,MAAMoB,UAAU,GAAG;IACjBC,GAAG,EAAEhB,iBAAiB,CAACO,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAES,GAAG,CAAC,GAAGT,MAAM,CAACS,GAAG,GAAG,gBAAgB;IACnEC,GAAG,EAAEjB,iBAAiB,CAACO,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEU,GAAG,CAAC,GAAGV,MAAM,CAACU,GAAG,GAAG;EACrD,CAAC;;EAED;EACA,IAAI,CAACjB,iBAAiB,CAACO,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAES,GAAG,CAAC,IAAI,CAAChB,iBAAiB,CAACO,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEU,GAAG,CAAC,EAAE;IACtEC,OAAO,CAACC,IAAI,CAAC,sDAAsD,EAAEZ,MAAM,EAAE,6BAA6B,EAAEQ,UAAU,CAAC;EACzH;EAEA,MAAMK,iBAAiB,GAAGA,CAACC,GAAG,EAAEC,IAAI,KAAK;IACvC,IAAI;MACF,IAAId,aAAa,IAAI,OAAOA,aAAa,KAAK,UAAU,EAAE;QACxDA,aAAa,CAACa,GAAG,EAAEC,IAAI,CAAC;MAC1B;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdL,OAAO,CAACK,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;IACjD;EACF,CAAC;EAED,oBACExB,OAAA,CAACL,cAAc;IACb8B,gBAAgB,EAAE;MAChBC,GAAG,EAAEd,cAAc,IAAId;IACzB,CAAE;IACF6B,WAAW,EAAE,EAAG;IAChBnB,MAAM,EAAEQ,UAAW;IACnBY,OAAO,EAAE;MACPC,iBAAiB,EAAE;IACrB,CAAE;IACFC,kCAAkC;IAClCC,iBAAiB,EAAEA,CAAC;MAAET,GAAG;MAAEC;IAAK,CAAC,KAAKF,iBAAiB,CAACC,GAAG,EAAEC,IAAI,CAAE;IAAAb,QAAA,EAElEA;EAAQ;IAAAsB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACK,CAAC;AAErB;AAACxB,EAAA,CA3CuBJ,eAAe;EAAA,QACVV,WAAW;AAAA;AAAAuC,EAAA,GADhB7B,eAAe;AAAA,IAAA6B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}