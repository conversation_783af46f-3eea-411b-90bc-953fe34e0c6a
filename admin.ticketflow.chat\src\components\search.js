import React, { useState, useMemo, useEffect, useRef } from 'react';
import debounce from 'lodash/debounce';
import { Select, Spin } from 'antd';

export const DebounceSelect = ({
  fetchOptions,
  debounceTimeout = 400,
  onClear,
  ...props
}) => {
  const [fetching, setFetching] = useState(false);
  const [options, setOptions] = useState([]);
  const isMountedRef = useRef(true);

  const debounceFetcher = useMemo(() => {
    const loadOptions = (value) => {
      if (!isMountedRef.current) return;

      setOptions([]);
      setFetching(true);
      fetchOptions(value)
        .then((newOptions) => {
          if (isMountedRef.current) {
            setOptions(newOptions);
            setFetching(false);
          }
        })
        .catch((error) => {
          if (isMountedRef.current) {
            console.error('DebounceSelect fetch error:', error);
            setFetching(false);
          }
        });
    };
    return debounce(loadOptions, debounceTimeout);
  }, [fetchOptions, debounceTimeout]);

  useEffect(() => {
    return () => {
      isMountedRef.current = false;
      // Cancel any pending debounced calls
      debounceFetcher.cancel();
    };
  }, [debounceFetcher]);

  const fetchOnFocus = () => {
      debounceFetcher('');
  };

  return (
    <Select
      showSearch
      allowClear
      labelInValue={true}
      filterOption={false}
      onSearch={debounceFetcher}
      onClear={() => {
        debounceFetcher('');
        !!onClear && onClear();
      }}
      notFoundContent={fetching ? <Spin size='small' /> : 'no results'}
      {...props}
      options={options}
      onFocus={fetchOnFocus}
    />
  );
};
