{"ast": null, "code": "import { LAT, LNG } from 'configs/app-global';\nconst isValidCoordinate = value => {\n  const num = Number(value);\n  return !isNaN(num) && isFinite(num);\n};\nexport default function getDefaultLocation(settings) {\n  // Default fallback coordinates\n  const defaultCoords = {\n    lat: 47.4143302506288,\n    lng: 8.532059477976883\n  };\n\n  // If no settings or location, return default\n  if (!(settings !== null && settings !== void 0 && settings.location) || typeof settings.location !== 'string') {\n    return defaultCoords;\n  }\n\n  // Try to parse the location string\n  const location = settings.location.split(', ');\n  const lat = location === null || location === void 0 ? void 0 : location[0];\n  const lng = location === null || location === void 0 ? void 0 : location[1];\n\n  // Validate both coordinates\n  if (isValidCoordinate(lat) && isValidCoordinate(lng)) {\n    return {\n      lat: Number(lat),\n      lng: Number(lng)\n    };\n  }\n\n  // If validation fails, return default coordinates\n  console.warn('Invalid location coordinates in settings:', settings.location, 'Using default coordinates');\n  return defaultCoords;\n}", "map": {"version": 3, "names": ["LAT", "LNG", "isValidCoordinate", "value", "num", "Number", "isNaN", "isFinite", "getDefaultLocation", "settings", "defaultCoords", "lat", "lng", "location", "split", "console", "warn"], "sources": ["C:/OSPanel/home/<USER>/src/helpers/getDefaultLocation.js"], "sourcesContent": ["import { LAT, LNG } from 'configs/app-global';\n\nconst isValidCoordinate = (value) => {\n  const num = Number(value);\n  return !isNaN(num) && isFinite(num);\n};\n\nexport default function getDefaultLocation(settings) {\n  // Default fallback coordinates\n  const defaultCoords = {\n    lat: 47.4143302506288,\n    lng: 8.532059477976883,\n  };\n\n  // If no settings or location, return default\n  if (!settings?.location || typeof settings.location !== 'string') {\n    return defaultCoords;\n  }\n\n  // Try to parse the location string\n  const location = settings.location.split(', ');\n  const lat = location?.[0];\n  const lng = location?.[1];\n\n  // Validate both coordinates\n  if (isValidCoordinate(lat) && isValidCoordinate(lng)) {\n    return {\n      lat: Number(lat),\n      lng: Number(lng),\n    };\n  }\n\n  // If validation fails, return default coordinates\n  console.warn('Invalid location coordinates in settings:', settings.location, 'Using default coordinates');\n  return defaultCoords;\n}\n"], "mappings": "AAAA,SAASA,GAAG,EAAEC,GAAG,QAAQ,oBAAoB;AAE7C,MAAMC,iBAAiB,GAAIC,KAAK,IAAK;EACnC,MAAMC,GAAG,GAAGC,MAAM,CAACF,KAAK,CAAC;EACzB,OAAO,CAACG,KAAK,CAACF,GAAG,CAAC,IAAIG,QAAQ,CAACH,GAAG,CAAC;AACrC,CAAC;AAED,eAAe,SAASI,kBAAkBA,CAACC,QAAQ,EAAE;EACnD;EACA,MAAMC,aAAa,GAAG;IACpBC,GAAG,EAAE,gBAAgB;IACrBC,GAAG,EAAE;EACP,CAAC;;EAED;EACA,IAAI,EAACH,QAAQ,aAARA,QAAQ,eAARA,QAAQ,CAAEI,QAAQ,KAAI,OAAOJ,QAAQ,CAACI,QAAQ,KAAK,QAAQ,EAAE;IAChE,OAAOH,aAAa;EACtB;;EAEA;EACA,MAAMG,QAAQ,GAAGJ,QAAQ,CAACI,QAAQ,CAACC,KAAK,CAAC,IAAI,CAAC;EAC9C,MAAMH,GAAG,GAAGE,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAG,CAAC,CAAC;EACzB,MAAMD,GAAG,GAAGC,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAG,CAAC,CAAC;;EAEzB;EACA,IAAIX,iBAAiB,CAACS,GAAG,CAAC,IAAIT,iBAAiB,CAACU,GAAG,CAAC,EAAE;IACpD,OAAO;MACLD,GAAG,EAAEN,MAAM,CAACM,GAAG,CAAC;MAChBC,GAAG,EAAEP,MAAM,CAACO,GAAG;IACjB,CAAC;EACH;;EAEA;EACAG,OAAO,CAACC,IAAI,CAAC,2CAA2C,EAAEP,QAAQ,CAACI,QAAQ,EAAE,2BAA2B,CAAC;EACzG,OAAOH,aAAa;AACtB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}