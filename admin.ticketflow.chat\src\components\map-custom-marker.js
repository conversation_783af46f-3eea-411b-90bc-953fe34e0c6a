import React from 'react';
import GoogleMapReact from 'google-map-react';
import { shallowEqual, useSelector } from 'react-redux';
import { MAP_API_KEY } from '../configs/app-global';

const isValidCoordinate = (value) => {
  const num = Number(value);
  return !isNaN(num) && isFinite(num);
};

export default function MapCustomMarker({ center, handleLoadMap, children }) {
  const { google_map_key } = useSelector(
    (state) => state.globalSettings.settings,
    shallowEqual
  );

  // Validate center coordinates and provide fallback
  const safeCenter = {
    lat: isValidCoordinate(center?.lat) ? center.lat : 47.4143302506288,
    lng: isValidCoordinate(center?.lng) ? center.lng : 8.532059477976883,
  };

  // Log warning if invalid center was provided
  if (!isValidCoordinate(center?.lat) || !isValidCoordinate(center?.lng)) {
    console.warn('MapCustomMarker received invalid center coordinates:', center, 'Using fallback coordinates:', safeCenter);
  }

  const safeHandleLoadMap = (map, maps) => {
    try {
      if (handleLoadMap && typeof handleLoadMap === 'function') {
        handleLoadMap(map, maps);
      }
    } catch (error) {
      console.error('Error in handleLoadMap:', error);
    }
  };

  return (
    <GoogleMapReact
      bootstrapURLKeys={{
        key: google_map_key || MAP_API_KEY,
      }}
      defaultZoom={12}
      center={safeCenter}
      options={{
        fullscreenControl: false,
      }}
      yesIWantToUseGoogleMapApiInternals
      onGoogleApiLoaded={({ map, maps }) => safeHandleLoadMap(map, maps)}
    >
      {children}
    </GoogleMapReact>
  );
}
