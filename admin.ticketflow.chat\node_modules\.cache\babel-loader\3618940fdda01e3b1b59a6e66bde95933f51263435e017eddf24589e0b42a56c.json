{"ast": null, "code": "var _jsxFileName = \"C:\\\\OSPanel\\\\home\\\\admin.ticketflow.chat\\\\src\\\\components\\\\map-error-boundary.js\";\nimport React from 'react';\nimport { Card, Alert, Button } from 'antd';\nimport { ReloadOutlined } from '@ant-design/icons';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nclass MapErrorBoundary extends React.Component {\n  constructor(props) {\n    super(props);\n    this.handleReload = () => {\n      // Reset the error boundary state\n      this.setState({\n        hasError: false,\n        error: null,\n        errorInfo: null\n      });\n      // Optionally reload the page\n      window.location.reload();\n    };\n    this.state = {\n      hasError: false,\n      error: null,\n      errorInfo: null\n    };\n  }\n  static getDerivedStateFromError() {\n    // Update state so the next render will show the fallback UI\n    return {\n      hasError: true\n    };\n  }\n  componentDidCatch(error, errorInfo) {\n    // Log the error for debugging\n    console.error('MapErrorBoundary caught an error:', error, errorInfo);\n\n    // Log additional details for JavaScript errors\n    if (error instanceof ReferenceError) {\n      console.error('ReferenceError caught in MapErrorBoundary:', error.message);\n    }\n    this.setState({\n      error: error,\n      errorInfo: errorInfo\n    });\n  }\n  render() {\n    if (this.state.hasError) {\n      // Fallback UI when map fails to load\n      return /*#__PURE__*/_jsxDEV(Card, {\n        title: \"Map Loading Error\",\n        style: {\n          height: '73vh',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            textAlign: 'center',\n            padding: '20px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Alert, {\n            message: \"Map Failed to Load\",\n            description: \"There was an error loading the map. This might be due to invalid coordinates or network issues.\",\n            type: \"error\",\n            showIcon: true,\n            style: {\n              marginBottom: '20px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 47,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            type: \"primary\",\n            icon: /*#__PURE__*/_jsxDEV(ReloadOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 56,\n              columnNumber: 21\n            }, this),\n            onClick: this.handleReload,\n            children: \"Reload Map\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 54,\n            columnNumber: 13\n          }, this), process.env.NODE_ENV === 'development' && /*#__PURE__*/_jsxDEV(\"details\", {\n            style: {\n              marginTop: '20px',\n              textAlign: 'left'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"summary\", {\n              children: \"Error Details (Development Only)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 63,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"pre\", {\n              style: {\n                fontSize: '12px',\n                color: '#666'\n              },\n              children: [this.state.error && this.state.error.toString(), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 66,\n                columnNumber: 19\n              }, this), this.state.errorInfo.componentStack]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 64,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 62,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 46,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 42,\n        columnNumber: 9\n      }, this);\n    }\n    return this.props.children;\n  }\n}\nexport default MapErrorBoundary;", "map": {"version": 3, "names": ["React", "Card", "<PERSON><PERSON>", "<PERSON><PERSON>", "ReloadOutlined", "jsxDEV", "_jsxDEV", "MapErrorBoundary", "Component", "constructor", "props", "handleReload", "setState", "<PERSON><PERSON><PERSON><PERSON>", "error", "errorInfo", "window", "location", "reload", "state", "getDerivedStateFromError", "componentDidCatch", "console", "ReferenceError", "message", "render", "title", "style", "height", "display", "alignItems", "justifyContent", "children", "textAlign", "padding", "description", "type", "showIcon", "marginBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "icon", "onClick", "process", "env", "NODE_ENV", "marginTop", "fontSize", "color", "toString", "componentStack"], "sources": ["C:/OSPanel/home/<USER>/src/components/map-error-boundary.js"], "sourcesContent": ["import React from 'react';\nimport { Card, Alert, Button } from 'antd';\nimport { ReloadOutlined } from '@ant-design/icons';\n\nclass MapErrorBoundary extends React.Component {\n  constructor(props) {\n    super(props);\n    this.state = { hasError: false, error: null, errorInfo: null };\n  }\n\n  static getDerivedStateFromError() {\n    // Update state so the next render will show the fallback UI\n    return { hasError: true };\n  }\n\n  componentDidCatch(error, errorInfo) {\n    // Log the error for debugging\n    console.error('MapErrorBoundary caught an error:', error, errorInfo);\n\n    // Log additional details for JavaScript errors\n    if (error instanceof ReferenceError) {\n      console.error('ReferenceError caught in MapErrorBoundary:', error.message);\n    }\n\n    this.setState({\n      error: error,\n      errorInfo: errorInfo\n    });\n  }\n\n  handleReload = () => {\n    // Reset the error boundary state\n    this.setState({ hasError: false, error: null, errorInfo: null });\n    // Optionally reload the page\n    window.location.reload();\n  };\n\n  render() {\n    if (this.state.hasError) {\n      // Fallback UI when map fails to load\n      return (\n        <Card \n          title=\"Map Loading Error\" \n          style={{ height: '73vh', display: 'flex', alignItems: 'center', justifyContent: 'center' }}\n        >\n          <div style={{ textAlign: 'center', padding: '20px' }}>\n            <Alert\n              message=\"Map Failed to Load\"\n              description=\"There was an error loading the map. This might be due to invalid coordinates or network issues.\"\n              type=\"error\"\n              showIcon\n              style={{ marginBottom: '20px' }}\n            />\n            <Button \n              type=\"primary\" \n              icon={<ReloadOutlined />} \n              onClick={this.handleReload}\n            >\n              Reload Map\n            </Button>\n            {process.env.NODE_ENV === 'development' && (\n              <details style={{ marginTop: '20px', textAlign: 'left' }}>\n                <summary>Error Details (Development Only)</summary>\n                <pre style={{ fontSize: '12px', color: '#666' }}>\n                  {this.state.error && this.state.error.toString()}\n                  <br />\n                  {this.state.errorInfo.componentStack}\n                </pre>\n              </details>\n            )}\n          </div>\n        </Card>\n      );\n    }\n\n    return this.props.children;\n  }\n}\n\nexport default MapErrorBoundary;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,EAAEC,KAAK,EAAEC,MAAM,QAAQ,MAAM;AAC1C,SAASC,cAAc,QAAQ,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnD,MAAMC,gBAAgB,SAASP,KAAK,CAACQ,SAAS,CAAC;EAC7CC,WAAWA,CAACC,KAAK,EAAE;IACjB,KAAK,CAACA,KAAK,CAAC;IAAC,KAwBfC,YAAY,GAAG,MAAM;MACnB;MACA,IAAI,CAACC,QAAQ,CAAC;QAAEC,QAAQ,EAAE,KAAK;QAAEC,KAAK,EAAE,IAAI;QAAEC,SAAS,EAAE;MAAK,CAAC,CAAC;MAChE;MACAC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC;IAC1B,CAAC;IA5BC,IAAI,CAACC,KAAK,GAAG;MAAEN,QAAQ,EAAE,KAAK;MAAEC,KAAK,EAAE,IAAI;MAAEC,SAAS,EAAE;IAAK,CAAC;EAChE;EAEA,OAAOK,wBAAwBA,CAAA,EAAG;IAChC;IACA,OAAO;MAAEP,QAAQ,EAAE;IAAK,CAAC;EAC3B;EAEAQ,iBAAiBA,CAACP,KAAK,EAAEC,SAAS,EAAE;IAClC;IACAO,OAAO,CAACR,KAAK,CAAC,mCAAmC,EAAEA,KAAK,EAAEC,SAAS,CAAC;;IAEpE;IACA,IAAID,KAAK,YAAYS,cAAc,EAAE;MACnCD,OAAO,CAACR,KAAK,CAAC,4CAA4C,EAAEA,KAAK,CAACU,OAAO,CAAC;IAC5E;IAEA,IAAI,CAACZ,QAAQ,CAAC;MACZE,KAAK,EAAEA,KAAK;MACZC,SAAS,EAAEA;IACb,CAAC,CAAC;EACJ;EASAU,MAAMA,CAAA,EAAG;IACP,IAAI,IAAI,CAACN,KAAK,CAACN,QAAQ,EAAE;MACvB;MACA,oBACEP,OAAA,CAACL,IAAI;QACHyB,KAAK,EAAC,mBAAmB;QACzBC,KAAK,EAAE;UAAEC,MAAM,EAAE,MAAM;UAAEC,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,QAAQ;UAAEC,cAAc,EAAE;QAAS,CAAE;QAAAC,QAAA,eAE3F1B,OAAA;UAAKqB,KAAK,EAAE;YAAEM,SAAS,EAAE,QAAQ;YAAEC,OAAO,EAAE;UAAO,CAAE;UAAAF,QAAA,gBACnD1B,OAAA,CAACJ,KAAK;YACJsB,OAAO,EAAC,oBAAoB;YAC5BW,WAAW,EAAC,iGAAiG;YAC7GC,IAAI,EAAC,OAAO;YACZC,QAAQ;YACRV,KAAK,EAAE;cAAEW,YAAY,EAAE;YAAO;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC,eACFpC,OAAA,CAACH,MAAM;YACLiC,IAAI,EAAC,SAAS;YACdO,IAAI,eAAErC,OAAA,CAACF,cAAc;cAAAmC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACzBE,OAAO,EAAE,IAAI,CAACjC,YAAa;YAAAqB,QAAA,EAC5B;UAED;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,EACRG,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,iBACrCzC,OAAA;YAASqB,KAAK,EAAE;cAAEqB,SAAS,EAAE,MAAM;cAAEf,SAAS,EAAE;YAAO,CAAE;YAAAD,QAAA,gBACvD1B,OAAA;cAAA0B,QAAA,EAAS;YAAgC;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAS,CAAC,eACnDpC,OAAA;cAAKqB,KAAK,EAAE;gBAAEsB,QAAQ,EAAE,MAAM;gBAAEC,KAAK,EAAE;cAAO,CAAE;cAAAlB,QAAA,GAC7C,IAAI,CAACb,KAAK,CAACL,KAAK,IAAI,IAAI,CAACK,KAAK,CAACL,KAAK,CAACqC,QAAQ,CAAC,CAAC,eAChD7C,OAAA;gBAAAiC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,EACL,IAAI,CAACvB,KAAK,CAACJ,SAAS,CAACqC,cAAc;YAAA;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CACV;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAEX;IAEA,OAAO,IAAI,CAAChC,KAAK,CAACsB,QAAQ;EAC5B;AACF;AAEA,eAAezB,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}