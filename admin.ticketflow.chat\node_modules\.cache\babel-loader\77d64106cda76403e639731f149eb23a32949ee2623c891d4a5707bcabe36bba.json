{"ast": null, "code": "var _jsxFileName = \"C:\\\\OSPanel\\\\home\\\\admin.ticketflow.chat\\\\src\\\\components\\\\search.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useMemo, useEffect, useRef } from 'react';\nimport debounce from 'lodash/debounce';\nimport { Select, Spin } from 'antd';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const DebounceSelect = ({\n  fetchOptions,\n  debounceTimeout = 400,\n  onClear,\n  ...props\n}) => {\n  _s();\n  const [fetching, setFetching] = useState(false);\n  const [options, setOptions] = useState([]);\n  const isMountedRef = useRef(true);\n  const debounceFetcher = useMemo(() => {\n    const loadOptions = value => {\n      if (!isMountedRef.current) return;\n      setOptions([]);\n      setFetching(true);\n      fetchOptions(value).then(newOptions => {\n        if (isMountedRef.current) {\n          setOptions(newOptions);\n          setFetching(false);\n        }\n      }).catch(error => {\n        if (isMountedRef.current) {\n          console.error('DebounceSelect fetch error:', error);\n          setFetching(false);\n        }\n      });\n    };\n    return debounce(loadOptions, debounceTimeout);\n  }, [fetchOptions, debounceTimeout]);\n  useEffect(() => {\n    return () => {\n      isMountedRef.current = false;\n      // Cancel any pending debounced calls\n      debounceFetcher.cancel();\n    };\n  }, [debounceFetcher]);\n  const fetchOnFocus = () => {\n    debounceFetcher('');\n  };\n  return /*#__PURE__*/_jsxDEV(Select, {\n    showSearch: true,\n    allowClear: true,\n    labelInValue: true,\n    filterOption: false,\n    onSearch: debounceFetcher,\n    onClear: () => {\n      debounceFetcher('');\n      !!onClear && onClear();\n    },\n    notFoundContent: fetching ? /*#__PURE__*/_jsxDEV(Spin, {\n      size: \"small\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 61,\n      columnNumber: 35\n    }, this) : 'no results',\n    ...props,\n    options: options,\n    onFocus: fetchOnFocus\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 51,\n    columnNumber: 5\n  }, this);\n};\n_s(DebounceSelect, \"fMHebGltCBnKRu83FThOcM2qzDc=\");\n_c = DebounceSelect;\nvar _c;\n$RefreshReg$(_c, \"DebounceSelect\");", "map": {"version": 3, "names": ["React", "useState", "useMemo", "useEffect", "useRef", "debounce", "Select", "Spin", "jsxDEV", "_jsxDEV", "DebounceSelect", "fetchOptions", "debounceTimeout", "onClear", "props", "_s", "fetching", "setFetching", "options", "setOptions", "isMountedRef", "deboun<PERSON><PERSON><PERSON><PERSON>", "loadOptions", "value", "current", "then", "newOptions", "catch", "error", "console", "cancel", "fetchOnFocus", "showSearch", "allowClear", "labelInValue", "filterOption", "onSearch", "notFoundContent", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onFocus", "_c", "$RefreshReg$"], "sources": ["C:/OSPanel/home/<USER>/src/components/search.js"], "sourcesContent": ["import React, { useState, useMemo, useEffect, useRef } from 'react';\nimport debounce from 'lodash/debounce';\nimport { Select, Spin } from 'antd';\n\nexport const DebounceSelect = ({\n  fetchOptions,\n  debounceTimeout = 400,\n  onClear,\n  ...props\n}) => {\n  const [fetching, setFetching] = useState(false);\n  const [options, setOptions] = useState([]);\n  const isMountedRef = useRef(true);\n\n  const debounceFetcher = useMemo(() => {\n    const loadOptions = (value) => {\n      if (!isMountedRef.current) return;\n\n      setOptions([]);\n      setFetching(true);\n      fetchOptions(value)\n        .then((newOptions) => {\n          if (isMountedRef.current) {\n            setOptions(newOptions);\n            setFetching(false);\n          }\n        })\n        .catch((error) => {\n          if (isMountedRef.current) {\n            console.error('DebounceSelect fetch error:', error);\n            setFetching(false);\n          }\n        });\n    };\n    return debounce(loadOptions, debounceTimeout);\n  }, [fetchOptions, debounceTimeout]);\n\n  useEffect(() => {\n    return () => {\n      isMountedRef.current = false;\n      // Cancel any pending debounced calls\n      debounceFetcher.cancel();\n    };\n  }, [debounceFetcher]);\n\n  const fetchOnFocus = () => {\n      debounceFetcher('');\n  };\n\n  return (\n    <Select\n      showSearch\n      allowClear\n      labelInValue={true}\n      filterOption={false}\n      onSearch={debounceFetcher}\n      onClear={() => {\n        debounceFetcher('');\n        !!onClear && onClear();\n      }}\n      notFoundContent={fetching ? <Spin size='small' /> : 'no results'}\n      {...props}\n      options={options}\n      onFocus={fetchOnFocus}\n    />\n  );\n};\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,OAAO,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AACnE,OAAOC,QAAQ,MAAM,iBAAiB;AACtC,SAASC,MAAM,EAAEC,IAAI,QAAQ,MAAM;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpC,OAAO,MAAMC,cAAc,GAAGA,CAAC;EAC7BC,YAAY;EACZC,eAAe,GAAG,GAAG;EACrBC,OAAO;EACP,GAAGC;AACL,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAACiB,OAAO,EAAEC,UAAU,CAAC,GAAGlB,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAMmB,YAAY,GAAGhB,MAAM,CAAC,IAAI,CAAC;EAEjC,MAAMiB,eAAe,GAAGnB,OAAO,CAAC,MAAM;IACpC,MAAMoB,WAAW,GAAIC,KAAK,IAAK;MAC7B,IAAI,CAACH,YAAY,CAACI,OAAO,EAAE;MAE3BL,UAAU,CAAC,EAAE,CAAC;MACdF,WAAW,CAAC,IAAI,CAAC;MACjBN,YAAY,CAACY,KAAK,CAAC,CAChBE,IAAI,CAAEC,UAAU,IAAK;QACpB,IAAIN,YAAY,CAACI,OAAO,EAAE;UACxBL,UAAU,CAACO,UAAU,CAAC;UACtBT,WAAW,CAAC,KAAK,CAAC;QACpB;MACF,CAAC,CAAC,CACDU,KAAK,CAAEC,KAAK,IAAK;QAChB,IAAIR,YAAY,CAACI,OAAO,EAAE;UACxBK,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;UACnDX,WAAW,CAAC,KAAK,CAAC;QACpB;MACF,CAAC,CAAC;IACN,CAAC;IACD,OAAOZ,QAAQ,CAACiB,WAAW,EAAEV,eAAe,CAAC;EAC/C,CAAC,EAAE,CAACD,YAAY,EAAEC,eAAe,CAAC,CAAC;EAEnCT,SAAS,CAAC,MAAM;IACd,OAAO,MAAM;MACXiB,YAAY,CAACI,OAAO,GAAG,KAAK;MAC5B;MACAH,eAAe,CAACS,MAAM,CAAC,CAAC;IAC1B,CAAC;EACH,CAAC,EAAE,CAACT,eAAe,CAAC,CAAC;EAErB,MAAMU,YAAY,GAAGA,CAAA,KAAM;IACvBV,eAAe,CAAC,EAAE,CAAC;EACvB,CAAC;EAED,oBACEZ,OAAA,CAACH,MAAM;IACL0B,UAAU;IACVC,UAAU;IACVC,YAAY,EAAE,IAAK;IACnBC,YAAY,EAAE,KAAM;IACpBC,QAAQ,EAAEf,eAAgB;IAC1BR,OAAO,EAAEA,CAAA,KAAM;MACbQ,eAAe,CAAC,EAAE,CAAC;MACnB,CAAC,CAACR,OAAO,IAAIA,OAAO,CAAC,CAAC;IACxB,CAAE;IACFwB,eAAe,EAAErB,QAAQ,gBAAGP,OAAA,CAACF,IAAI;MAAC+B,IAAI,EAAC;IAAO;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,GAAG,YAAa;IAAA,GAC7D5B,KAAK;IACTI,OAAO,EAAEA,OAAQ;IACjByB,OAAO,EAAEZ;EAAa;IAAAQ,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACvB,CAAC;AAEN,CAAC;AAAC3B,EAAA,CA9DWL,cAAc;AAAAkC,EAAA,GAAdlC,cAAc;AAAA,IAAAkC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}