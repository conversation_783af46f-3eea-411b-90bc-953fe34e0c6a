{"__meta": {"id": "X8bf21fd23f82728380734251b08ae0be", "datetime": "2025-07-20 23:05:10", "utime": 1753063510.502315, "method": "GET", "uri": "/api/v1/dashboard/admin/payment-to-partners?lang=pt-BR&date_from=2025-06-20&date_to=2025-07-20&type=seller", "ip": "127.0.0.1"}, "php": {"version": "8.2.26", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[23:05:10] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\OSPanel\\home\\api.ticketflow.chat\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1753063510.393275, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753063510.102852, "end": 1753063510.502334, "duration": 0.39948201179504395, "duration_str": "399ms", "measures": [{"label": "Booting", "start": 1753063510.102852, "relative_start": 0, "end": 1753063510.374718, "relative_end": 1753063510.374718, "duration": 0.2718658447265625, "duration_str": "272ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1753063510.37473, "relative_start": 0.2718780040740967, "end": 1753063510.502336, "relative_end": 1.9073486328125e-06, "duration": 0.12760591506958008, "duration_str": "128ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 43208664, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET api/v1/dashboard/admin/payment-to-partners", "middleware": "api, block.ip, sanctum.check, role:admin|manager", "as": "admin.payment-to-partners.index", "controller": "App\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\PaymentToPartnerController@index", "namespace": null, "prefix": "api/v1/dashboard/admin", "where": [], "file": "<a href=\"phpstorm://open?file=C:\\OSPanel\\home\\api.ticketflow.chat\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\PaymentToPartnerController.php&line=25\">\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\PaymentToPartnerController.php:25-30</a>"}, "queries": {"nb_statements": 10, "nb_failed_statements": 0, "accumulated_duration": 0.02526, "accumulated_duration_str": "25.26ms", "statements": [{"sql": "select `locale`, `default` from `languages` where `default` = 1 and `languages`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Repositories\\CoreRepository.php", "line": 51}, {"index": 16, "namespace": null, "name": "\\app\\Repositories\\CoreRepository.php", "line": 24}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 917}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 758}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 853}], "duration": 0.019559999999999998, "duration_str": "19.56ms", "stmt_id": "\\app\\Repositories\\CoreRepository.php:51", "connection": "foodyman", "start_percent": 0, "width_percent": 77.435}, {"sql": "select `id`, `default` from `currencies` where `default` = 1 and `currencies`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Repositories\\CoreRepository.php", "line": 41}, {"index": 16, "namespace": null, "name": "\\app\\Repositories\\CoreRepository.php", "line": 25}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 917}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 758}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 853}], "duration": 0.00067, "duration_str": "670μs", "stmt_id": "\\app\\Repositories\\CoreRepository.php:41", "connection": "foodyman", "start_percent": 77.435, "width_percent": 2.652}, {"sql": "select `locale`, `default` from `languages` where `default` = 1 and `languages`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 57}, {"index": 16, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 28}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 917}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 758}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 853}], "duration": 0.00047, "duration_str": "470μs", "stmt_id": "\\app\\Services\\CoreService.php:57", "connection": "foodyman", "start_percent": 80.087, "width_percent": 1.861}, {"sql": "select `id`, `default` from `currencies` where `default` = 1 and `currencies`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 44}, {"index": 16, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 29}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 917}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 758}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 853}], "duration": 0.00033, "duration_str": "330μs", "stmt_id": "\\app\\Services\\CoreService.php:44", "connection": "foodyman", "start_percent": 81.948, "width_percent": 1.306}, {"sql": "select * from `personal_access_tokens` where `personal_access_tokens`.`id` = '53' limit 1", "type": "query", "params": [], "bindings": ["53"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\vendor\\laravel\\sanctum\\src\\PersonalAccessToken.php", "line": 64}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 67}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 24, "namespace": "middleware", "name": "sanctum.check", "line": 17}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}], "duration": 0.0005899999999999999, "duration_str": "590μs", "stmt_id": "\\vendor\\laravel\\sanctum\\src\\PersonalAccessToken.php:64", "connection": "foodyman", "start_percent": 83.254, "width_percent": 2.336}, {"sql": "select * from `users` where `users`.`id` = 101 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["101"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 137}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 69}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 25, "namespace": "middleware", "name": "sanctum.check", "line": 17}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}], "duration": 0.0007700000000000001, "duration_str": "770μs", "stmt_id": "\\vendor\\laravel\\sanctum\\src\\Guard.php:137", "connection": "foodyman", "start_percent": 85.59, "width_percent": 3.048}, {"sql": "update `personal_access_tokens` set `last_used_at` = '2025-07-20 23:05:10', `personal_access_tokens`.`updated_at` = '2025-07-20 23:05:10' where `id` = 53", "type": "query", "params": [], "bindings": ["2025-07-20 23:05:10", "2025-07-20 23:05:10", "53"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 83}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 19, "namespace": "middleware", "name": "sanctum.check", "line": 17}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 21, "namespace": "middleware", "name": "block.ip", "line": 24}], "duration": 0.0014399999999999999, "duration_str": "1.44ms", "stmt_id": "\\vendor\\laravel\\sanctum\\src\\Guard.php:83", "connection": "foodyman", "start_percent": 88.638, "width_percent": 5.701}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (101) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 188}, {"index": 22, "namespace": null, "name": "\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 235}, {"index": 23, "namespace": "middleware", "name": "role", "line": 29}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 25, "namespace": "middleware", "name": "sanctum.check", "line": 18}], "duration": 0.00039, "duration_str": "390μs", "stmt_id": "\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php:188", "connection": "foodyman", "start_percent": 94.339, "width_percent": 1.544}, {"sql": "select count(*) as aggregate from `languages` where `locale` = 'pt-BR'", "type": "query", "params": [], "bindings": ["pt-BR"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 55}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 810}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 781}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 610}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 416}], "duration": 0.00055, "duration_str": "550μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php:55", "connection": "foodyman", "start_percent": 95.883, "width_percent": 2.177}, {"sql": "select count(*) as aggregate from `payment_to_partners` where `type` = 'seller' and date(`created_at`) >= '2025-06-20' and date(`created_at`) <= '2025-07-20' and `payment_to_partners`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["seller", "2025-06-20", "2025-07-20"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Repositories\\PaymentToPartnerRepository\\PaymentToPartnerRepository.php", "line": 38}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\PaymentToPartnerController.php", "line": 27}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00049, "duration_str": "490μs", "stmt_id": "\\app\\Repositories\\PaymentToPartnerRepository\\PaymentToPartnerRepository.php:38", "connection": "foodyman", "start_percent": 98.06, "width_percent": 1.94}]}, "models": {"data": {"Spatie\\Permission\\Models\\Role": 1, "App\\Models\\User": 1, "Laravel\\Sanctum\\PersonalAccessToken": 1, "App\\Models\\Currency": 2, "App\\Models\\Language": 2}, "count": 7}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": [], "request": {"telescope": "<a href=\"http://localhost:8000/_debugbar/telescope/9f70b0c8-020a-4127-b56a-e1833ca8bfe7\" target=\"_blank\">View in Telescope</a>", "path_info": "/api/v1/dashboard/admin/payment-to-partners", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-887921383 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>lang</span>\" => \"<span class=sf-dump-str title=\"5 characters\">pt-BR</span>\"\n  \"<span class=sf-dump-key>date_from</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-20</span>\"\n  \"<span class=sf-dump-key>date_to</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-07-20</span>\"\n  \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">seller</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-887921383\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1711281096 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>lang</span>\" => \"<span class=sf-dump-str title=\"5 characters\">pt-BR</span>\"\n  \"<span class=sf-dump-key>date_from</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-20</span>\"\n  \"<span class=sf-dump-key>date_to</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-07-20</span>\"\n  \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">seller</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1711281096\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1649664979 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>authorization</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"50 characters\">Bearer 53|oq8Lhbh74w4ClrUO63oz294UJQPO9SXbtML8m7xn</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:3000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">same-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost:3000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"44 characters\">pt-BR,pt;q=0.9,en-US;q=0.8,en;q=0.7,it;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1649664979\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-328234533 data-indent-pad=\"  \"><span class=sf-dump-note>array:33</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"42 characters\">C:\\OSPanel\\home\\api.ticketflow.chat\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">63888</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.26 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"106 characters\">/api/v1/dashboard/admin/payment-to-partners?lang=pt-BR&amp;date_from=2025-06-20&amp;date_to=2025-07-20&amp;type=seller</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"52 characters\">C:\\OSPanel\\home\\api.ticketflow.chat\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"43 characters\">/api/v1/dashboard/admin/payment-to-partners</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"53 characters\">/index.php/api/v1/dashboard/admin/payment-to-partners</span>\"\n  \"<span class=sf-dump-key>QUERY_STRING</span>\" => \"<span class=sf-dump-str title=\"62 characters\">lang=pt-BR&amp;date_from=2025-06-20&amp;date_to=2025-07-20&amp;type=seller</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_AUTHORIZATION</span>\" => \"<span class=sf-dump-str title=\"50 characters\">Bearer 53|oq8Lhbh74w4ClrUO63oz294UJQPO9SXbtML8m7xn</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_ORIGIN</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:3000</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"9 characters\">same-site</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost:3000/</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"44 characters\">pt-BR,pt;q=0.9,en-US;q=0.8,en;q=0.7,it;q=0.6</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1753063510.1029</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1753063510</span>\n  \"<span class=sf-dump-key>argv</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"62 characters\">lang=pt-BR&amp;date_from=2025-06-20&amp;date_to=2025-07-20&amp;type=seller</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>argc</span>\" => <span class=sf-dump-num>1</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-328234533\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-666205462 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-666205462\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1615335216 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 21 Jul 2025 02:05:10 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-limit</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-num>5000</span>\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-remaining</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-num>4990</span>\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1615335216\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-694850786 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-694850786\", {\"maxDepth\":0})</script>\n"}}