{"ast": null, "code": "var _jsxFileName = \"C:\\\\OSPanel\\\\home\\\\admin.ticketflow.chat\\\\src\\\\components\\\\sidebar.js\",\n  _s = $RefreshSig$();\nimport React, { useMemo, useState, useRef, useEffect } from 'react';\nimport { Link, useLocation, useNavigate } from 'react-router-dom';\nimport { LogoutOutlined, MenuFoldOutlined, MenuUnfoldOutlined, SearchOutlined } from '@ant-design/icons';\nimport { Divider, Menu, Space, Layout, Modal, Input } from 'antd';\nimport { shallowEqual, useDispatch, useSelector } from 'react-redux';\nimport { addMenu, clearMenu, setMenu } from '../redux/slices/menu';\nimport { useTranslation } from 'react-i18next';\nimport LangModal from './lang-modal';\nimport getSystemIcons from '../helpers/getSystemIcons';\nimport NotificationBar from './notificationBar';\nimport { navCollapseTrigger } from '../redux/slices/theme';\nimport ThemeConfigurator from './theme-configurator';\nimport i18n from '../configs/i18next';\nimport { RiArrowDownSFill } from 'react-icons/ri';\nimport Scrollbars from 'react-custom-scrollbars';\nimport SubMenu from 'antd/lib/menu/SubMenu';\nimport NavProfile from './nav-profile';\nimport { batch } from 'react-redux';\nimport { clearUser } from '../redux/slices/auth';\nimport { setCurrentChat } from '../redux/slices/chat';\nimport { data as allRoutes } from 'configs/menu-config';\nimport useDidUpdate from 'helpers/useDidUpdate';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst {\n  Sider\n} = Layout;\nconst Sidebar = () => {\n  _s();\n  var _languages$find;\n  const {\n    t\n  } = useTranslation();\n  const navigate = useNavigate();\n  const {\n    pathname\n  } = useLocation();\n  const {\n    user\n  } = useSelector(state => state.auth, shallowEqual);\n  const [isModalVisible, setIsModalVisible] = useState(false);\n  const isMountedRef = useRef(true);\n  const {\n    system_refund,\n    payment_type,\n    by_subscription\n  } = useSelector(state => state.globalSettings.settings, shallowEqual);\n  const {\n    navCollapsed\n  } = useSelector(state => state.theme.theme, shallowEqual);\n  const {\n    languages\n  } = useSelector(state => state.formLang, shallowEqual);\n  const dispatch = useDispatch();\n  const [langModal, setLangModal] = useState(false);\n  const {\n    myShop\n  } = useSelector(state => state.myShop, shallowEqual);\n  const {\n    theme\n  } = useSelector(state => state.theme, shallowEqual);\n  const parcelMode = useMemo(() => !!theme.parcelMode && (user === null || user === void 0 ? void 0 : user.role) === 'admin', [theme, user]);\n  const routes = useMemo(() => {\n    var _user$urls;\n    const isSubscriptionEnabled = by_subscription === '1';\n    const excludeRoutes = {\n      routes: [],\n      subRoutes: []\n    };\n    if (!isSubscriptionEnabled) {\n      excludeRoutes.subRoutes.push('subscriptions', 'my.subscriptions', 'shop.subscriptions');\n    }\n    return filterUserRoutes((_user$urls = user.urls) === null || _user$urls === void 0 ? void 0 : _user$urls.filter(item => !excludeRoutes.routes.includes(item.name)).map(item => {\n      var _item$submenu;\n      return {\n        ...item,\n        submenu: (_item$submenu = item.submenu) === null || _item$submenu === void 0 ? void 0 : _item$submenu.filter(sub => !excludeRoutes.subRoutes.includes(sub.name))\n      };\n    }));\n  }, [user]);\n  const active = routes === null || routes === void 0 ? void 0 : routes.find(item => pathname.includes(item.url));\n  const [searchTerm, setSearchTerm] = useState('');\n  const [data, setData] = useState(parcelMode ? allRoutes.parcel : routes);\n  useDidUpdate(() => {\n    if (isMountedRef.current) {\n      if (parcelMode) {\n        setData(allRoutes.parcel);\n      } else {\n        setData(routes);\n      }\n    }\n  }, [theme, user]);\n\n  // Cleanup effect\n  useEffect(() => {\n    return () => {\n      isMountedRef.current = false;\n    };\n  }, []);\n  const addNewItem = item => {\n    if (typeof item.url === 'undefined') return;\n    if (item.name === 'logout') {\n      if (isMountedRef.current) {\n        setIsModalVisible(true);\n      }\n      return;\n    }\n    const data = {\n      ...item,\n      icon: undefined,\n      children: undefined,\n      refetch: true\n    };\n    dispatch(setMenu(data));\n    navigate(`/${item.url}`);\n  };\n  function filterUserRoutes(routes) {\n    let list = routes;\n    if (myShop.type === 'shop') {\n      list = routes === null || routes === void 0 ? void 0 : routes.filter(item => (item === null || item === void 0 ? void 0 : item.name) !== 'brands');\n    }\n    if (payment_type === 'admin') {\n      list = routes === null || routes === void 0 ? void 0 : routes.filter(item => (item === null || item === void 0 ? void 0 : item.name) !== 'payments');\n    }\n    if (system_refund === '0') {\n      list = routes === null || routes === void 0 ? void 0 : routes.filter(item => (item === null || item === void 0 ? void 0 : item.name) !== 'refunds');\n    }\n    return list;\n  }\n  const menuTrigger = event => {\n    event.stopPropagation();\n    dispatch(navCollapseTrigger());\n  };\n  const addMenuItem = payload => {\n    const data = {\n      ...payload,\n      icon: undefined\n    };\n    dispatch(addMenu(data));\n  };\n  const handleOk = () => {\n    batch(() => {\n      dispatch(clearUser());\n      dispatch(clearMenu());\n      dispatch(setCurrentChat(null));\n    });\n    if (isMountedRef.current) {\n      setIsModalVisible(false);\n    }\n    localStorage.removeItem('token');\n    navigate('/login');\n  };\n  const handleCancel = () => {\n    if (isMountedRef.current) {\n      setIsModalVisible(false);\n    }\n  };\n  function getOptionList(routes) {\n    const optionTree = [];\n    routes === null || routes === void 0 ? void 0 : routes.map(item => {\n      var _item$submenu2;\n      optionTree.push(item);\n      item === null || item === void 0 ? void 0 : (_item$submenu2 = item.submenu) === null || _item$submenu2 === void 0 ? void 0 : _item$submenu2.map(sub => {\n        var _sub$children;\n        optionTree.push(sub);\n        sub === null || sub === void 0 ? void 0 : (_sub$children = sub.children) === null || _sub$children === void 0 ? void 0 : _sub$children.map(child => {\n          optionTree.push(child);\n        });\n      });\n    });\n    return optionTree;\n  }\n  const optionList = getOptionList(data);\n  const menuList = searchTerm.length > 0 ? optionList.filter(input => {\n    var _input$name;\n    return t((_input$name = input === null || input === void 0 ? void 0 : input.name) !== null && _input$name !== void 0 ? _input$name : '').toUpperCase().includes(searchTerm.toUpperCase());\n  }) : data;\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(Sider, {\n      className: \"navbar-nav side-nav\",\n      width: 250,\n      collapsed: navCollapsed,\n      style: {\n        height: '100vh',\n        top: 0\n      },\n      children: [/*#__PURE__*/_jsxDEV(NavProfile, {\n        user: user\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 190,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"menu-collapse\",\n        onClick: menuTrigger,\n        children: /*#__PURE__*/_jsxDEV(MenuFoldOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 192,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 191,\n        columnNumber: 9\n      }, this), navCollapsed && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-content-center\",\n        children: /*#__PURE__*/_jsxDEV(ThemeConfigurator, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 196,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 195,\n        columnNumber: 11\n      }, this), !navCollapsed ? /*#__PURE__*/_jsxDEV(Space, {\n        className: \"mx-4 mt-2 d-flex justify-content-between\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"icon-button\",\n          onClick: () => isMountedRef.current && setLangModal(true),\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            className: \"globalOutlined\",\n            src: (_languages$find = languages.find(item => item.locale === i18n.language)) === null || _languages$find === void 0 ? void 0 : _languages$find.img,\n            alt: user.fullName\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 203,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"default-lang\",\n            children: i18n.language\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 210,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(RiArrowDownSFill, {\n            size: 15\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 211,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 202,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"d-flex\",\n          children: [/*#__PURE__*/_jsxDEV(ThemeConfigurator, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 214,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(NotificationBar, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 215,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 213,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 201,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"menu-unfold\",\n        onClick: menuTrigger,\n        children: /*#__PURE__*/_jsxDEV(MenuUnfoldOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 220,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 219,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Divider, {\n        style: {\n          margin: '10px 0'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 223,\n        columnNumber: 9\n      }, this), !navCollapsed && /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"mt-2 mb-2 d-flex justify-content-center\",\n        children: /*#__PURE__*/_jsxDEV(Input, {\n          placeholder: \"search\",\n          style: {\n            width: '90%'\n          },\n          value: searchTerm,\n          onChange: event => {\n            setSearchTerm(event.target.value);\n          },\n          prefix: /*#__PURE__*/_jsxDEV(SearchOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 234,\n            columnNumber: 23\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 227,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 226,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Scrollbars, {\n        autoHeight: true,\n        autoHeightMin: window.innerHeight > 969 ? '80vh' : '77vh',\n        autoHeightMax: window.innerHeight > 969 ? '80vh' : '77vh',\n        autoHide: true,\n        children: /*#__PURE__*/_jsxDEV(Menu, {\n          theme: \"light\",\n          mode: \"inline\",\n          defaultSelectedKeys: [String(active === null || active === void 0 ? void 0 : active.id)],\n          defaultOpenKeys: !navCollapsed ? data === null || data === void 0 ? void 0 : data.map((i, idx) => i.id + '_' + idx) : [],\n          children: menuList === null || menuList === void 0 ? void 0 : menuList.map((item, idx) => {\n            var _item$submenu3;\n            return ((_item$submenu3 = item.submenu) === null || _item$submenu3 === void 0 ? void 0 : _item$submenu3.length) > 0 ? /*#__PURE__*/_jsxDEV(SubMenu, {\n              title: t(item.name),\n              icon: getSystemIcons(item.icon),\n              children: item.submenu.map((submenu, idy) => {\n                var _submenu$children, _submenu$children2;\n                return ((_submenu$children = submenu.children) === null || _submenu$children === void 0 ? void 0 : _submenu$children.length) > 0 ? /*#__PURE__*/_jsxDEV(SubMenu, {\n                  defaultOpen: true,\n                  title: t(submenu.name),\n                  icon: getSystemIcons(submenu.icon),\n                  onTitleClick: () => addNewItem(submenu),\n                  children: (_submenu$children2 = submenu.children) === null || _submenu$children2 === void 0 ? void 0 : _submenu$children2.map((sub, idk) => /*#__PURE__*/_jsxDEV(Menu.Item, {\n                    icon: getSystemIcons(sub.icon),\n                    children: /*#__PURE__*/_jsxDEV(Link, {\n                      to: '/' + sub.url,\n                      onClick: () => addMenuItem(sub),\n                      children: /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: t(sub.name)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 278,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 274,\n                      columnNumber: 29\n                    }, this)\n                  }, 'child' + idk + sub.id, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 270,\n                    columnNumber: 27\n                  }, this))\n                }, submenu.id + '_' + idy, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 262,\n                  columnNumber: 23\n                }, this) : /*#__PURE__*/_jsxDEV(Menu.Item, {\n                  icon: getSystemIcons(submenu.icon),\n                  children: /*#__PURE__*/_jsxDEV(Link, {\n                    to: '/' + submenu.url,\n                    onClick: () => addNewItem(submenu),\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: t(submenu.name)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 292,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 288,\n                    columnNumber: 25\n                  }, this)\n                }, submenu.id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 284,\n                  columnNumber: 23\n                }, this);\n              })\n            }, item.id + '_' + idx, false, {\n              fileName: _jsxFileName,\n              lineNumber: 255,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(Menu.Item, {\n              icon: getSystemIcons(item.icon),\n              children: /*#__PURE__*/_jsxDEV(Link, {\n                to: '/' + item.url,\n                onClick: () => addNewItem(item),\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: t(item.name)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 301,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 300,\n                columnNumber: 19\n              }, this)\n            }, item.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 299,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 245,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 239,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 184,\n      columnNumber: 7\n    }, this), langModal && /*#__PURE__*/_jsxDEV(LangModal, {\n      visible: langModal,\n      handleCancel: () => setLangModal(false)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 311,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      visible: isModalVisible,\n      onOk: handleOk,\n      onCancel: handleCancel,\n      centered: true,\n      children: [/*#__PURE__*/_jsxDEV(LogoutOutlined, {\n        style: {\n          fontSize: '25px',\n          color: '#08c'\n        },\n        theme: \"primary\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 323,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"ml-2\",\n        children: t('leave.site')\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 327,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 317,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(Sidebar, \"Ro7bFrvvh2p9QNKd54pNb/YlXVQ=\", false, function () {\n  return [useTranslation, useNavigate, useLocation, useSelector, useSelector, useSelector, useSelector, useDispatch, useSelector, useSelector, useDidUpdate];\n});\n_c = Sidebar;\nexport default Sidebar;\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");", "map": {"version": 3, "names": ["React", "useMemo", "useState", "useRef", "useEffect", "Link", "useLocation", "useNavigate", "LogoutOutlined", "MenuFoldOutlined", "MenuUnfoldOutlined", "SearchOutlined", "Divider", "<PERSON><PERSON>", "Space", "Layout", "Modal", "Input", "shallowEqual", "useDispatch", "useSelector", "addMenu", "clearMenu", "setMenu", "useTranslation", "LangModal", "getSystemIcons", "NotificationBar", "navCollapseTrigger", "ThemeConfigurator", "i18n", "RiArrowDownSFill", "Scrollbars", "SubMenu", "NavProfile", "batch", "clearUser", "setCurrentChat", "data", "allRoutes", "useDidUpdate", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "<PERSON><PERSON>", "Sidebar", "_s", "_languages$find", "t", "navigate", "pathname", "user", "state", "auth", "isModalVisible", "setIsModalVisible", "isMountedRef", "system_refund", "payment_type", "by_subscription", "globalSettings", "settings", "navCollapsed", "theme", "languages", "formLang", "dispatch", "langModal", "setLangModal", "myShop", "parcelMode", "role", "routes", "_user$urls", "isSubscriptionEnabled", "excludeRoutes", "subRoutes", "push", "filterUserRoutes", "urls", "filter", "item", "includes", "name", "map", "_item$submenu", "submenu", "sub", "active", "find", "url", "searchTerm", "setSearchTerm", "setData", "parcel", "current", "addNewItem", "icon", "undefined", "children", "refetch", "list", "type", "menuTrigger", "event", "stopPropagation", "addMenuItem", "payload", "handleOk", "localStorage", "removeItem", "handleCancel", "getOptionList", "optionTree", "_item$submenu2", "_sub$children", "child", "optionList", "menuList", "length", "input", "_input$name", "toUpperCase", "className", "width", "collapsed", "style", "height", "top", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "src", "locale", "language", "img", "alt", "fullName", "size", "margin", "placeholder", "value", "onChange", "target", "prefix", "autoHeight", "autoHeightMin", "window", "innerHeight", "autoHeightMax", "autoHide", "mode", "defaultSelectedKeys", "String", "id", "defaultOpenKeys", "i", "idx", "_item$submenu3", "title", "idy", "_submenu$children", "_submenu$children2", "defaultOpen", "onTitleClick", "idk", "<PERSON><PERSON>", "to", "visible", "onOk", "onCancel", "centered", "fontSize", "color", "_c", "$RefreshReg$"], "sources": ["C:/OSPanel/home/<USER>/src/components/sidebar.js"], "sourcesContent": ["import React, { useMemo, useState, useRef, useEffect } from 'react';\nimport { Link, useLocation, useNavigate } from 'react-router-dom';\nimport {\n  LogoutOutlined,\n  MenuFoldOutlined,\n  MenuUnfoldOutlined,\n  SearchOutlined,\n} from '@ant-design/icons';\nimport { Divider, Menu, Space, Layout, Modal, Input } from 'antd';\nimport { shallowEqual, useDispatch, useSelector } from 'react-redux';\nimport { addMenu, clearMenu, setMenu } from '../redux/slices/menu';\nimport { useTranslation } from 'react-i18next';\nimport LangModal from './lang-modal';\nimport getSystemIcons from '../helpers/getSystemIcons';\nimport NotificationBar from './notificationBar';\nimport { navCollapseTrigger } from '../redux/slices/theme';\nimport ThemeConfigurator from './theme-configurator';\nimport i18n from '../configs/i18next';\nimport { RiArrowDownSFill } from 'react-icons/ri';\nimport Scrollbars from 'react-custom-scrollbars';\nimport SubMenu from 'antd/lib/menu/SubMenu';\nimport NavProfile from './nav-profile';\nimport { batch } from 'react-redux';\nimport { clearUser } from '../redux/slices/auth';\nimport { setCurrentChat } from '../redux/slices/chat';\nimport { data as allRoutes } from 'configs/menu-config';\nimport useDidUpdate from 'helpers/useDidUpdate';\nconst { Sider } = Layout;\n\nconst Sidebar = () => {\n  const { t } = useTranslation();\n  const navigate = useNavigate();\n  const { pathname } = useLocation();\n  const { user } = useSelector((state) => state.auth, shallowEqual);\n  const [isModalVisible, setIsModalVisible] = useState(false);\n  const isMountedRef = useRef(true);\n  const { system_refund, payment_type, by_subscription } = useSelector(\n    (state) => state.globalSettings.settings,\n    shallowEqual,\n  );\n  const { navCollapsed } = useSelector(\n    (state) => state.theme.theme,\n    shallowEqual,\n  );\n  const { languages } = useSelector((state) => state.formLang, shallowEqual);\n  const dispatch = useDispatch();\n  const [langModal, setLangModal] = useState(false);\n  const { myShop } = useSelector((state) => state.myShop, shallowEqual);\n  const { theme } = useSelector((state) => state.theme, shallowEqual);\n  const parcelMode = useMemo(\n    () => !!theme.parcelMode && user?.role === 'admin',\n    [theme, user],\n  );\n  const routes = useMemo(() => {\n    const isSubscriptionEnabled = by_subscription === '1';\n    const excludeRoutes = { routes: [], subRoutes: [] };\n    if (!isSubscriptionEnabled) {\n      excludeRoutes.subRoutes.push(\n        'subscriptions',\n        'my.subscriptions',\n        'shop.subscriptions',\n      );\n    }\n    return filterUserRoutes(\n      user.urls\n        ?.filter((item) => !excludeRoutes.routes.includes(item.name))\n        .map((item) => ({\n          ...item,\n          submenu: item.submenu?.filter(\n            (sub) => !excludeRoutes.subRoutes.includes(sub.name),\n          ),\n        })),\n    );\n  }, [user]);\n  const active = routes?.find((item) => pathname.includes(item.url));\n  const [searchTerm, setSearchTerm] = useState('');\n  const [data, setData] = useState(parcelMode ? allRoutes.parcel : routes);\n\n  useDidUpdate(() => {\n    if (isMountedRef.current) {\n      if (parcelMode) {\n        setData(allRoutes.parcel);\n      } else {\n        setData(routes);\n      }\n    }\n  }, [theme, user]);\n\n  // Cleanup effect\n  useEffect(() => {\n    return () => {\n      isMountedRef.current = false;\n    };\n  }, []);\n\n  const addNewItem = (item) => {\n    if (typeof item.url === 'undefined') return;\n    if (item.name === 'logout') {\n      if (isMountedRef.current) {\n        setIsModalVisible(true);\n      }\n      return;\n    }\n    const data = {\n      ...item,\n      icon: undefined,\n      children: undefined,\n      refetch: true,\n    };\n    dispatch(setMenu(data));\n    navigate(`/${item.url}`);\n  };\n\n  function filterUserRoutes(routes) {\n    let list = routes;\n    if (myShop.type === 'shop') {\n      list = routes?.filter((item) => item?.name !== 'brands');\n    }\n    if (payment_type === 'admin') {\n      list = routes?.filter((item) => item?.name !== 'payments');\n    }\n    if (system_refund === '0') {\n      list = routes?.filter((item) => item?.name !== 'refunds');\n    }\n    return list;\n  }\n\n  const menuTrigger = (event) => {\n    event.stopPropagation();\n    dispatch(navCollapseTrigger());\n  };\n\n  const addMenuItem = (payload) => {\n    const data = { ...payload, icon: undefined };\n    dispatch(addMenu(data));\n  };\n\n  const handleOk = () => {\n    batch(() => {\n      dispatch(clearUser());\n      dispatch(clearMenu());\n      dispatch(setCurrentChat(null));\n    });\n    if (isMountedRef.current) {\n      setIsModalVisible(false);\n    }\n    localStorage.removeItem('token');\n    navigate('/login');\n  };\n\n  const handleCancel = () => {\n    if (isMountedRef.current) {\n      setIsModalVisible(false);\n    }\n  };\n\n  function getOptionList(routes) {\n    const optionTree = [];\n    routes?.map((item) => {\n      optionTree.push(item);\n      item?.submenu?.map((sub) => {\n        optionTree.push(sub);\n        sub?.children?.map((child) => {\n          optionTree.push(child);\n        });\n      });\n    });\n    return optionTree;\n  }\n\n  const optionList = getOptionList(data);\n\n  const menuList =\n    searchTerm.length > 0\n      ? optionList.filter((input) =>\n          t(input?.name ?? '')\n            .toUpperCase()\n            .includes(searchTerm.toUpperCase()),\n        )\n      : data;\n\n  return (\n    <>\n      <Sider\n        className='navbar-nav side-nav'\n        width={250}\n        collapsed={navCollapsed}\n        style={{ height: '100vh', top: 0 }}\n      >\n        <NavProfile user={user} />\n        <div className='menu-collapse' onClick={menuTrigger}>\n          <MenuFoldOutlined />\n        </div>\n        {navCollapsed && (\n          <div className='flex justify-content-center'>\n            <ThemeConfigurator />\n          </div>\n        )}\n\n        {!navCollapsed ? (\n          <Space className='mx-4 mt-2 d-flex justify-content-between'>\n            <span className='icon-button' onClick={() => isMountedRef.current && setLangModal(true)}>\n              <img\n                className='globalOutlined'\n                src={\n                  languages.find((item) => item.locale === i18n.language)?.img\n                }\n                alt={user.fullName}\n              />\n              <span className='default-lang'>{i18n.language}</span>\n              <RiArrowDownSFill size={15} />\n            </span>\n            <span className='d-flex'>\n              <ThemeConfigurator />\n              <NotificationBar />\n            </span>\n          </Space>\n        ) : (\n          <div className='menu-unfold' onClick={menuTrigger}>\n            <MenuUnfoldOutlined />\n          </div>\n        )}\n        <Divider style={{ margin: '10px 0' }} />\n\n        {!navCollapsed && (\n          <span className='mt-2 mb-2 d-flex justify-content-center'>\n            <Input\n              placeholder='search'\n              style={{ width: '90%' }}\n              value={searchTerm}\n              onChange={(event) => {\n                setSearchTerm(event.target.value);\n              }}\n              prefix={<SearchOutlined />}\n            />\n          </span>\n        )}\n\n        <Scrollbars\n          autoHeight\n          autoHeightMin={window.innerHeight > 969 ? '80vh' : '77vh'}\n          autoHeightMax={window.innerHeight > 969 ? '80vh' : '77vh'}\n          autoHide\n        >\n          <Menu\n            theme='light'\n            mode='inline'\n            defaultSelectedKeys={[String(active?.id)]}\n            defaultOpenKeys={\n              !navCollapsed ? data?.map((i, idx) => i.id + '_' + idx) : []\n            }\n          >\n            {menuList?.map((item, idx) =>\n              item.submenu?.length > 0 ? (\n                <SubMenu\n                  key={item.id + '_' + idx}\n                  title={t(item.name)}\n                  icon={getSystemIcons(item.icon)}\n                >\n                  {item.submenu.map((submenu, idy) =>\n                    submenu.children?.length > 0 ? (\n                      <SubMenu\n                        defaultOpen={true}\n                        key={submenu.id + '_' + idy}\n                        title={t(submenu.name)}\n                        icon={getSystemIcons(submenu.icon)}\n                        onTitleClick={() => addNewItem(submenu)}\n                      >\n                        {submenu.children?.map((sub, idk) => (\n                          <Menu.Item\n                            key={'child' + idk + sub.id}\n                            icon={getSystemIcons(sub.icon)}\n                          >\n                            <Link\n                              to={'/' + sub.url}\n                              onClick={() => addMenuItem(sub)}\n                            >\n                              <span>{t(sub.name)}</span>\n                            </Link>\n                          </Menu.Item>\n                        ))}\n                      </SubMenu>\n                    ) : (\n                      <Menu.Item\n                        key={submenu.id}\n                        icon={getSystemIcons(submenu.icon)}\n                      >\n                        <Link\n                          to={'/' + submenu.url}\n                          onClick={() => addNewItem(submenu)}\n                        >\n                          <span>{t(submenu.name)}</span>\n                        </Link>\n                      </Menu.Item>\n                    ),\n                  )}\n                </SubMenu>\n              ) : (\n                <Menu.Item key={item.id} icon={getSystemIcons(item.icon)}>\n                  <Link to={'/' + item.url} onClick={() => addNewItem(item)}>\n                    <span>{t(item.name)}</span>\n                  </Link>\n                </Menu.Item>\n              ),\n            )}\n          </Menu>\n        </Scrollbars>\n      </Sider>\n\n      {langModal && (\n        <LangModal\n          visible={langModal}\n          handleCancel={() => setLangModal(false)}\n        />\n      )}\n\n      <Modal\n        visible={isModalVisible}\n        onOk={handleOk}\n        onCancel={handleCancel}\n        centered\n      >\n        <LogoutOutlined\n          style={{ fontSize: '25px', color: '#08c' }}\n          theme='primary'\n        />\n        <span className='ml-2'>{t('leave.site')}</span>\n      </Modal>\n    </>\n  );\n};\nexport default Sidebar;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,OAAO,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AACnE,SAASC,IAAI,EAAEC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AACjE,SACEC,cAAc,EACdC,gBAAgB,EAChBC,kBAAkB,EAClBC,cAAc,QACT,mBAAmB;AAC1B,SAASC,OAAO,EAAEC,IAAI,EAAEC,KAAK,EAAEC,MAAM,EAAEC,KAAK,EAAEC,KAAK,QAAQ,MAAM;AACjE,SAASC,YAAY,EAAEC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACpE,SAASC,OAAO,EAAEC,SAAS,EAAEC,OAAO,QAAQ,sBAAsB;AAClE,SAASC,cAAc,QAAQ,eAAe;AAC9C,OAAOC,SAAS,MAAM,cAAc;AACpC,OAAOC,cAAc,MAAM,2BAA2B;AACtD,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,SAASC,kBAAkB,QAAQ,uBAAuB;AAC1D,OAAOC,iBAAiB,MAAM,sBAAsB;AACpD,OAAOC,IAAI,MAAM,oBAAoB;AACrC,SAASC,gBAAgB,QAAQ,gBAAgB;AACjD,OAAOC,UAAU,MAAM,yBAAyB;AAChD,OAAOC,OAAO,MAAM,uBAAuB;AAC3C,OAAOC,UAAU,MAAM,eAAe;AACtC,SAASC,KAAK,QAAQ,aAAa;AACnC,SAASC,SAAS,QAAQ,sBAAsB;AAChD,SAASC,cAAc,QAAQ,sBAAsB;AACrD,SAASC,IAAI,IAAIC,SAAS,QAAQ,qBAAqB;AACvD,OAAOC,YAAY,MAAM,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAChD,MAAM;EAAEC;AAAM,CAAC,GAAG9B,MAAM;AAExB,MAAM+B,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,eAAA;EACpB,MAAM;IAAEC;EAAE,CAAC,GAAGzB,cAAc,CAAC,CAAC;EAC9B,MAAM0B,QAAQ,GAAG3C,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAE4C;EAAS,CAAC,GAAG7C,WAAW,CAAC,CAAC;EAClC,MAAM;IAAE8C;EAAK,CAAC,GAAGhC,WAAW,CAAEiC,KAAK,IAAKA,KAAK,CAACC,IAAI,EAAEpC,YAAY,CAAC;EACjE,MAAM,CAACqC,cAAc,EAAEC,iBAAiB,CAAC,GAAGtD,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAMuD,YAAY,GAAGtD,MAAM,CAAC,IAAI,CAAC;EACjC,MAAM;IAAEuD,aAAa;IAAEC,YAAY;IAAEC;EAAgB,CAAC,GAAGxC,WAAW,CACjEiC,KAAK,IAAKA,KAAK,CAACQ,cAAc,CAACC,QAAQ,EACxC5C,YACF,CAAC;EACD,MAAM;IAAE6C;EAAa,CAAC,GAAG3C,WAAW,CACjCiC,KAAK,IAAKA,KAAK,CAACW,KAAK,CAACA,KAAK,EAC5B9C,YACF,CAAC;EACD,MAAM;IAAE+C;EAAU,CAAC,GAAG7C,WAAW,CAAEiC,KAAK,IAAKA,KAAK,CAACa,QAAQ,EAAEhD,YAAY,CAAC;EAC1E,MAAMiD,QAAQ,GAAGhD,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACiD,SAAS,EAAEC,YAAY,CAAC,GAAGnE,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM;IAAEoE;EAAO,CAAC,GAAGlD,WAAW,CAAEiC,KAAK,IAAKA,KAAK,CAACiB,MAAM,EAAEpD,YAAY,CAAC;EACrE,MAAM;IAAE8C;EAAM,CAAC,GAAG5C,WAAW,CAAEiC,KAAK,IAAKA,KAAK,CAACW,KAAK,EAAE9C,YAAY,CAAC;EACnE,MAAMqD,UAAU,GAAGtE,OAAO,CACxB,MAAM,CAAC,CAAC+D,KAAK,CAACO,UAAU,IAAI,CAAAnB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoB,IAAI,MAAK,OAAO,EAClD,CAACR,KAAK,EAAEZ,IAAI,CACd,CAAC;EACD,MAAMqB,MAAM,GAAGxE,OAAO,CAAC,MAAM;IAAA,IAAAyE,UAAA;IAC3B,MAAMC,qBAAqB,GAAGf,eAAe,KAAK,GAAG;IACrD,MAAMgB,aAAa,GAAG;MAAEH,MAAM,EAAE,EAAE;MAAEI,SAAS,EAAE;IAAG,CAAC;IACnD,IAAI,CAACF,qBAAqB,EAAE;MAC1BC,aAAa,CAACC,SAAS,CAACC,IAAI,CAC1B,eAAe,EACf,kBAAkB,EAClB,oBACF,CAAC;IACH;IACA,OAAOC,gBAAgB,EAAAL,UAAA,GACrBtB,IAAI,CAAC4B,IAAI,cAAAN,UAAA,uBAATA,UAAA,CACIO,MAAM,CAAEC,IAAI,IAAK,CAACN,aAAa,CAACH,MAAM,CAACU,QAAQ,CAACD,IAAI,CAACE,IAAI,CAAC,CAAC,CAC5DC,GAAG,CAAEH,IAAI;MAAA,IAAAI,aAAA;MAAA,OAAM;QACd,GAAGJ,IAAI;QACPK,OAAO,GAAAD,aAAA,GAAEJ,IAAI,CAACK,OAAO,cAAAD,aAAA,uBAAZA,aAAA,CAAcL,MAAM,CAC1BO,GAAG,IAAK,CAACZ,aAAa,CAACC,SAAS,CAACM,QAAQ,CAACK,GAAG,CAACJ,IAAI,CACrD;MACF,CAAC;IAAA,CAAC,CACN,CAAC;EACH,CAAC,EAAE,CAAChC,IAAI,CAAC,CAAC;EACV,MAAMqC,MAAM,GAAGhB,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEiB,IAAI,CAAER,IAAI,IAAK/B,QAAQ,CAACgC,QAAQ,CAACD,IAAI,CAACS,GAAG,CAAC,CAAC;EAClE,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAG3F,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACoC,IAAI,EAAEwD,OAAO,CAAC,GAAG5F,QAAQ,CAACqE,UAAU,GAAGhC,SAAS,CAACwD,MAAM,GAAGtB,MAAM,CAAC;EAExEjC,YAAY,CAAC,MAAM;IACjB,IAAIiB,YAAY,CAACuC,OAAO,EAAE;MACxB,IAAIzB,UAAU,EAAE;QACduB,OAAO,CAACvD,SAAS,CAACwD,MAAM,CAAC;MAC3B,CAAC,MAAM;QACLD,OAAO,CAACrB,MAAM,CAAC;MACjB;IACF;EACF,CAAC,EAAE,CAACT,KAAK,EAAEZ,IAAI,CAAC,CAAC;;EAEjB;EACAhD,SAAS,CAAC,MAAM;IACd,OAAO,MAAM;MACXqD,YAAY,CAACuC,OAAO,GAAG,KAAK;IAC9B,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,UAAU,GAAIf,IAAI,IAAK;IAC3B,IAAI,OAAOA,IAAI,CAACS,GAAG,KAAK,WAAW,EAAE;IACrC,IAAIT,IAAI,CAACE,IAAI,KAAK,QAAQ,EAAE;MAC1B,IAAI3B,YAAY,CAACuC,OAAO,EAAE;QACxBxC,iBAAiB,CAAC,IAAI,CAAC;MACzB;MACA;IACF;IACA,MAAMlB,IAAI,GAAG;MACX,GAAG4C,IAAI;MACPgB,IAAI,EAAEC,SAAS;MACfC,QAAQ,EAAED,SAAS;MACnBE,OAAO,EAAE;IACX,CAAC;IACDlC,QAAQ,CAAC5C,OAAO,CAACe,IAAI,CAAC,CAAC;IACvBY,QAAQ,CAAE,IAAGgC,IAAI,CAACS,GAAI,EAAC,CAAC;EAC1B,CAAC;EAED,SAASZ,gBAAgBA,CAACN,MAAM,EAAE;IAChC,IAAI6B,IAAI,GAAG7B,MAAM;IACjB,IAAIH,MAAM,CAACiC,IAAI,KAAK,MAAM,EAAE;MAC1BD,IAAI,GAAG7B,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEQ,MAAM,CAAEC,IAAI,IAAK,CAAAA,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEE,IAAI,MAAK,QAAQ,CAAC;IAC1D;IACA,IAAIzB,YAAY,KAAK,OAAO,EAAE;MAC5B2C,IAAI,GAAG7B,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEQ,MAAM,CAAEC,IAAI,IAAK,CAAAA,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEE,IAAI,MAAK,UAAU,CAAC;IAC5D;IACA,IAAI1B,aAAa,KAAK,GAAG,EAAE;MACzB4C,IAAI,GAAG7B,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEQ,MAAM,CAAEC,IAAI,IAAK,CAAAA,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEE,IAAI,MAAK,SAAS,CAAC;IAC3D;IACA,OAAOkB,IAAI;EACb;EAEA,MAAME,WAAW,GAAIC,KAAK,IAAK;IAC7BA,KAAK,CAACC,eAAe,CAAC,CAAC;IACvBvC,QAAQ,CAACvC,kBAAkB,CAAC,CAAC,CAAC;EAChC,CAAC;EAED,MAAM+E,WAAW,GAAIC,OAAO,IAAK;IAC/B,MAAMtE,IAAI,GAAG;MAAE,GAAGsE,OAAO;MAAEV,IAAI,EAAEC;IAAU,CAAC;IAC5ChC,QAAQ,CAAC9C,OAAO,CAACiB,IAAI,CAAC,CAAC;EACzB,CAAC;EAED,MAAMuE,QAAQ,GAAGA,CAAA,KAAM;IACrB1E,KAAK,CAAC,MAAM;MACVgC,QAAQ,CAAC/B,SAAS,CAAC,CAAC,CAAC;MACrB+B,QAAQ,CAAC7C,SAAS,CAAC,CAAC,CAAC;MACrB6C,QAAQ,CAAC9B,cAAc,CAAC,IAAI,CAAC,CAAC;IAChC,CAAC,CAAC;IACF,IAAIoB,YAAY,CAACuC,OAAO,EAAE;MACxBxC,iBAAiB,CAAC,KAAK,CAAC;IAC1B;IACAsD,YAAY,CAACC,UAAU,CAAC,OAAO,CAAC;IAChC7D,QAAQ,CAAC,QAAQ,CAAC;EACpB,CAAC;EAED,MAAM8D,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAIvD,YAAY,CAACuC,OAAO,EAAE;MACxBxC,iBAAiB,CAAC,KAAK,CAAC;IAC1B;EACF,CAAC;EAED,SAASyD,aAAaA,CAACxC,MAAM,EAAE;IAC7B,MAAMyC,UAAU,GAAG,EAAE;IACrBzC,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEY,GAAG,CAAEH,IAAI,IAAK;MAAA,IAAAiC,cAAA;MACpBD,UAAU,CAACpC,IAAI,CAACI,IAAI,CAAC;MACrBA,IAAI,aAAJA,IAAI,wBAAAiC,cAAA,GAAJjC,IAAI,CAAEK,OAAO,cAAA4B,cAAA,uBAAbA,cAAA,CAAe9B,GAAG,CAAEG,GAAG,IAAK;QAAA,IAAA4B,aAAA;QAC1BF,UAAU,CAACpC,IAAI,CAACU,GAAG,CAAC;QACpBA,GAAG,aAAHA,GAAG,wBAAA4B,aAAA,GAAH5B,GAAG,CAAEY,QAAQ,cAAAgB,aAAA,uBAAbA,aAAA,CAAe/B,GAAG,CAAEgC,KAAK,IAAK;UAC5BH,UAAU,CAACpC,IAAI,CAACuC,KAAK,CAAC;QACxB,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,OAAOH,UAAU;EACnB;EAEA,MAAMI,UAAU,GAAGL,aAAa,CAAC3E,IAAI,CAAC;EAEtC,MAAMiF,QAAQ,GACZ3B,UAAU,CAAC4B,MAAM,GAAG,CAAC,GACjBF,UAAU,CAACrC,MAAM,CAAEwC,KAAK;IAAA,IAAAC,WAAA;IAAA,OACtBzE,CAAC,EAAAyE,WAAA,GAACD,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAErC,IAAI,cAAAsC,WAAA,cAAAA,WAAA,GAAI,EAAE,CAAC,CACjBC,WAAW,CAAC,CAAC,CACbxC,QAAQ,CAACS,UAAU,CAAC+B,WAAW,CAAC,CAAC,CAAC;EAAA,CACvC,CAAC,GACDrF,IAAI;EAEV,oBACEI,OAAA,CAAAE,SAAA;IAAAwD,QAAA,gBACE1D,OAAA,CAACG,KAAK;MACJ+E,SAAS,EAAC,qBAAqB;MAC/BC,KAAK,EAAE,GAAI;MACXC,SAAS,EAAE/D,YAAa;MACxBgE,KAAK,EAAE;QAAEC,MAAM,EAAE,OAAO;QAAEC,GAAG,EAAE;MAAE,CAAE;MAAA7B,QAAA,gBAEnC1D,OAAA,CAACR,UAAU;QAACkB,IAAI,EAAEA;MAAK;QAAA8E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC1B3F,OAAA;QAAKkF,SAAS,EAAC,eAAe;QAACU,OAAO,EAAE9B,WAAY;QAAAJ,QAAA,eAClD1D,OAAA,CAACjC,gBAAgB;UAAAyH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CAAC,EACLtE,YAAY,iBACXrB,OAAA;QAAKkF,SAAS,EAAC,6BAA6B;QAAAxB,QAAA,eAC1C1D,OAAA,CAACb,iBAAiB;UAAAqG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClB,CACN,EAEA,CAACtE,YAAY,gBACZrB,OAAA,CAAC5B,KAAK;QAAC8G,SAAS,EAAC,0CAA0C;QAAAxB,QAAA,gBACzD1D,OAAA;UAAMkF,SAAS,EAAC,aAAa;UAACU,OAAO,EAAEA,CAAA,KAAM7E,YAAY,CAACuC,OAAO,IAAI3B,YAAY,CAAC,IAAI,CAAE;UAAA+B,QAAA,gBACtF1D,OAAA;YACEkF,SAAS,EAAC,gBAAgB;YAC1BW,GAAG,GAAAvF,eAAA,GACDiB,SAAS,CAACyB,IAAI,CAAER,IAAI,IAAKA,IAAI,CAACsD,MAAM,KAAK1G,IAAI,CAAC2G,QAAQ,CAAC,cAAAzF,eAAA,uBAAvDA,eAAA,CAAyD0F,GAC1D;YACDC,GAAG,EAAEvF,IAAI,CAACwF;UAAS;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpB,CAAC,eACF3F,OAAA;YAAMkF,SAAS,EAAC,cAAc;YAAAxB,QAAA,EAAEtE,IAAI,CAAC2G;UAAQ;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACrD3F,OAAA,CAACX,gBAAgB;YAAC8G,IAAI,EAAE;UAAG;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC,eACP3F,OAAA;UAAMkF,SAAS,EAAC,QAAQ;UAAAxB,QAAA,gBACtB1D,OAAA,CAACb,iBAAiB;YAAAqG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACrB3F,OAAA,CAACf,eAAe;YAAAuG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,gBAER3F,OAAA;QAAKkF,SAAS,EAAC,aAAa;QAACU,OAAO,EAAE9B,WAAY;QAAAJ,QAAA,eAChD1D,OAAA,CAAChC,kBAAkB;UAAAwH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB,CACN,eACD3F,OAAA,CAAC9B,OAAO;QAACmH,KAAK,EAAE;UAAEe,MAAM,EAAE;QAAS;MAAE;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EAEvC,CAACtE,YAAY,iBACZrB,OAAA;QAAMkF,SAAS,EAAC,yCAAyC;QAAAxB,QAAA,eACvD1D,OAAA,CAACzB,KAAK;UACJ8H,WAAW,EAAC,QAAQ;UACpBhB,KAAK,EAAE;YAAEF,KAAK,EAAE;UAAM,CAAE;UACxBmB,KAAK,EAAEpD,UAAW;UAClBqD,QAAQ,EAAGxC,KAAK,IAAK;YACnBZ,aAAa,CAACY,KAAK,CAACyC,MAAM,CAACF,KAAK,CAAC;UACnC,CAAE;UACFG,MAAM,eAAEzG,OAAA,CAAC/B,cAAc;YAAAuH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACP,eAED3F,OAAA,CAACV,UAAU;QACToH,UAAU;QACVC,aAAa,EAAEC,MAAM,CAACC,WAAW,GAAG,GAAG,GAAG,MAAM,GAAG,MAAO;QAC1DC,aAAa,EAAEF,MAAM,CAACC,WAAW,GAAG,GAAG,GAAG,MAAM,GAAG,MAAO;QAC1DE,QAAQ;QAAArD,QAAA,eAER1D,OAAA,CAAC7B,IAAI;UACHmD,KAAK,EAAC,OAAO;UACb0F,IAAI,EAAC,QAAQ;UACbC,mBAAmB,EAAE,CAACC,MAAM,CAACnE,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEoE,EAAE,CAAC,CAAE;UAC1CC,eAAe,EACb,CAAC/F,YAAY,GAAGzB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE+C,GAAG,CAAC,CAAC0E,CAAC,EAAEC,GAAG,KAAKD,CAAC,CAACF,EAAE,GAAG,GAAG,GAAGG,GAAG,CAAC,GAAG,EAC3D;UAAA5D,QAAA,EAEAmB,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAElC,GAAG,CAAC,CAACH,IAAI,EAAE8E,GAAG;YAAA,IAAAC,cAAA;YAAA,OACvB,EAAAA,cAAA,GAAA/E,IAAI,CAACK,OAAO,cAAA0E,cAAA,uBAAZA,cAAA,CAAczC,MAAM,IAAG,CAAC,gBACtB9E,OAAA,CAACT,OAAO;cAENiI,KAAK,EAAEjH,CAAC,CAACiC,IAAI,CAACE,IAAI,CAAE;cACpBc,IAAI,EAAExE,cAAc,CAACwD,IAAI,CAACgB,IAAI,CAAE;cAAAE,QAAA,EAE/BlB,IAAI,CAACK,OAAO,CAACF,GAAG,CAAC,CAACE,OAAO,EAAE4E,GAAG;gBAAA,IAAAC,iBAAA,EAAAC,kBAAA;gBAAA,OAC7B,EAAAD,iBAAA,GAAA7E,OAAO,CAACa,QAAQ,cAAAgE,iBAAA,uBAAhBA,iBAAA,CAAkB5C,MAAM,IAAG,CAAC,gBAC1B9E,OAAA,CAACT,OAAO;kBACNqI,WAAW,EAAE,IAAK;kBAElBJ,KAAK,EAAEjH,CAAC,CAACsC,OAAO,CAACH,IAAI,CAAE;kBACvBc,IAAI,EAAExE,cAAc,CAAC6D,OAAO,CAACW,IAAI,CAAE;kBACnCqE,YAAY,EAAEA,CAAA,KAAMtE,UAAU,CAACV,OAAO,CAAE;kBAAAa,QAAA,GAAAiE,kBAAA,GAEvC9E,OAAO,CAACa,QAAQ,cAAAiE,kBAAA,uBAAhBA,kBAAA,CAAkBhF,GAAG,CAAC,CAACG,GAAG,EAAEgF,GAAG,kBAC9B9H,OAAA,CAAC7B,IAAI,CAAC4J,IAAI;oBAERvE,IAAI,EAAExE,cAAc,CAAC8D,GAAG,CAACU,IAAI,CAAE;oBAAAE,QAAA,eAE/B1D,OAAA,CAACrC,IAAI;sBACHqK,EAAE,EAAE,GAAG,GAAGlF,GAAG,CAACG,GAAI;sBAClB2C,OAAO,EAAEA,CAAA,KAAM3B,WAAW,CAACnB,GAAG,CAAE;sBAAAY,QAAA,eAEhC1D,OAAA;wBAAA0D,QAAA,EAAOnD,CAAC,CAACuC,GAAG,CAACJ,IAAI;sBAAC;wBAAA8C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtB;kBAAC,GARF,OAAO,GAAGmC,GAAG,GAAGhF,GAAG,CAACqE,EAAE;oBAAA3B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OASlB,CACZ;gBAAC,GAjBG9C,OAAO,CAACsE,EAAE,GAAG,GAAG,GAAGM,GAAG;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAkBpB,CAAC,gBAEV3F,OAAA,CAAC7B,IAAI,CAAC4J,IAAI;kBAERvE,IAAI,EAAExE,cAAc,CAAC6D,OAAO,CAACW,IAAI,CAAE;kBAAAE,QAAA,eAEnC1D,OAAA,CAACrC,IAAI;oBACHqK,EAAE,EAAE,GAAG,GAAGnF,OAAO,CAACI,GAAI;oBACtB2C,OAAO,EAAEA,CAAA,KAAMrC,UAAU,CAACV,OAAO,CAAE;oBAAAa,QAAA,eAEnC1D,OAAA;sBAAA0D,QAAA,EAAOnD,CAAC,CAACsC,OAAO,CAACH,IAAI;oBAAC;sBAAA8C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1B;gBAAC,GARF9C,OAAO,CAACsE,EAAE;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OASN,CACZ;cAAA,CACH;YAAC,GAxCInD,IAAI,CAAC2E,EAAE,GAAG,GAAG,GAAGG,GAAG;cAAA9B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAyCjB,CAAC,gBAEV3F,OAAA,CAAC7B,IAAI,CAAC4J,IAAI;cAAevE,IAAI,EAAExE,cAAc,CAACwD,IAAI,CAACgB,IAAI,CAAE;cAAAE,QAAA,eACvD1D,OAAA,CAACrC,IAAI;gBAACqK,EAAE,EAAE,GAAG,GAAGxF,IAAI,CAACS,GAAI;gBAAC2C,OAAO,EAAEA,CAAA,KAAMrC,UAAU,CAACf,IAAI,CAAE;gBAAAkB,QAAA,eACxD1D,OAAA;kBAAA0D,QAAA,EAAOnD,CAAC,CAACiC,IAAI,CAACE,IAAI;gBAAC;kBAAA8C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB;YAAC,GAHOnD,IAAI,CAAC2E,EAAE;cAAA3B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAIZ,CACZ;UAAA,CACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC,EAEPjE,SAAS,iBACR1B,OAAA,CAACjB,SAAS;MACRkJ,OAAO,EAAEvG,SAAU;MACnB4C,YAAY,EAAEA,CAAA,KAAM3C,YAAY,CAAC,KAAK;IAAE;MAAA6D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzC,CACF,eAED3F,OAAA,CAAC1B,KAAK;MACJ2J,OAAO,EAAEpH,cAAe;MACxBqH,IAAI,EAAE/D,QAAS;MACfgE,QAAQ,EAAE7D,YAAa;MACvB8D,QAAQ;MAAA1E,QAAA,gBAER1D,OAAA,CAAClC,cAAc;QACbuH,KAAK,EAAE;UAAEgD,QAAQ,EAAE,MAAM;UAAEC,KAAK,EAAE;QAAO,CAAE;QAC3ChH,KAAK,EAAC;MAAS;QAAAkE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChB,CAAC,eACF3F,OAAA;QAAMkF,SAAS,EAAC,MAAM;QAAAxB,QAAA,EAAEnD,CAAC,CAAC,YAAY;MAAC;QAAAiF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1C,CAAC;EAAA,eACR,CAAC;AAEP,CAAC;AAACtF,EAAA,CA7SID,OAAO;EAAA,QACGtB,cAAc,EACXjB,WAAW,EACPD,WAAW,EACfc,WAAW,EAG6BA,WAAW,EAI3CA,WAAW,EAIdA,WAAW,EAChBD,WAAW,EAETC,WAAW,EACZA,WAAW,EA8B7BoB,YAAY;AAAA;AAAAyI,EAAA,GAjDRnI,OAAO;AA8Sb,eAAeA,OAAO;AAAC,IAAAmI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}