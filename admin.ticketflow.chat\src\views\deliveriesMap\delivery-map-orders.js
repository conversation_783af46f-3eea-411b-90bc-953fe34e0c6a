import React from 'react';
import { Card, Col, Row } from 'antd';
import { useTranslation } from 'react-i18next';
import { shallowEqual, useSelector } from 'react-redux';
import MapCustomMarker from '../../components/map-custom-marker';
import OrderCard from './order-card';
import OrderData from './order-data';
import getDefaultLocation from '../../helpers/getDefaultLocation';
import { IMG_URL } from '../../configs/app-global';
import userIcon from '../../assets/images/user.jpg';
import shopIcon from '../../assets/images/shop.png';
import courierIcon from '../../assets/images/courier.png';

const CourierMarker = () => (
  <div
    style={{
      position: 'absolute',
      transform: 'translate(-50%, -100%)',
    }}
  >
    <img src={courierIcon} width={80} alt='user' />
  </div>
);
const ShopMarker = () => (
  <div
    style={{
      position: 'absolute',
      transform: 'translate(-50%, -100%)',
    }}
  >
    <img src={shopIcon} width={50} alt='shop' />
  </div>
);
const UserMarker = () => (
  <div
    style={{
      position: 'absolute',
      transform: 'translate(-50%, -100%)',
    }}
  >
    <img src={userIcon} width={50} alt='user' />
  </div>
);

export default function DeliveryMapOrders() {
  const { t } = useTranslation();
  const { activeMenu } = useSelector((state) => state.menu, shallowEqual);
  const list = activeMenu?.data?.list || [];
  const data = activeMenu?.data?.item;
  const deliveryman = activeMenu?.data?.deliveryman;
  const { settings } = useSelector(
    (state) => state.globalSettings,
    shallowEqual,
  );
  const center = getDefaultLocation(settings);

  const isValidCoordinate = (value) => {
    const num = Number(value);
    return !isNaN(num) && isFinite(num) && num !== 0;
  };

  const handleLoadMap = (map, maps) => {
    const potentialMarkers = [
      {
        lat: deliveryman?.delivery_man_setting?.location?.latitude,
        lng: deliveryman?.delivery_man_setting?.location?.longitude,
      },
      {
        lat: data?.location?.latitude,
        lng: data?.location?.longitude,
      },
      {
        lat: data?.shop?.location?.latitude,
        lng: data?.shop?.location?.longitude,
      },
    ];

    const markers = potentialMarkers
      .filter((marker) => isValidCoordinate(marker.lat) && isValidCoordinate(marker.lng))
      .map((marker) => ({
        lat: Number(marker.lat),
        lng: Number(marker.lng),
      }));

    if (markers.length > 0) {
      let bounds = new maps.LatLngBounds();
      for (var i = 0; i < markers.length; i++) {
        bounds.extend(markers[i]);
      }
      map.fitBounds(bounds);
    }
  };

  return (
    <Card title={t('active.orders')} className='delivery'>
      <Row gutter={8}>
        <Col span={6}>
          <div className='order-list'>
            {list.map((item, index) => (
              <OrderCard
                key={item.id + index}
                data={item}
                active={data?.id === item.id}
              />
            ))}
          </div>
        </Col>
        <Col span={18}>
          <Card className='map-user-card with-order'>
            <OrderData data={deliveryman} order={data} />
          </Card>
          <div
            className='map-container'
            style={{ height: '65vh', width: '100%' }}
          >
            <MapCustomMarker
              key={'map' + data?.id}
              center={center}
              handleLoadMap={handleLoadMap}
            >
              {isValidCoordinate(deliveryman?.delivery_man_setting?.location?.latitude) &&
                isValidCoordinate(deliveryman?.delivery_man_setting?.location?.longitude) && (
                <CourierMarker
                  lat={Number(deliveryman.delivery_man_setting.location.latitude)}
                  lng={Number(deliveryman.delivery_man_setting.location.longitude)}
                  url={IMG_URL + deliveryman?.img}
                />
              )}
              {isValidCoordinate(data?.location?.latitude) &&
                isValidCoordinate(data?.location?.longitude) && (
                <UserMarker
                  lat={Number(data.location.latitude)}
                  lng={Number(data.location.longitude)}
                />
              )}
              {isValidCoordinate(data?.shop?.location?.latitude) &&
                isValidCoordinate(data?.shop?.location?.longitude) && (
                <ShopMarker
                  lat={Number(data.shop.location.latitude)}
                  lng={Number(data.shop.location.longitude)}
                />
              )}
            </MapCustomMarker>
          </div>
        </Col>
      </Row>
    </Card>
  );
}
