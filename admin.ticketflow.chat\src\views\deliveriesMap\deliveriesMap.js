import React, { useEffect, useState, useRef } from 'react';
import { Avatar, Card, Col, Row, Select } from 'antd';
import { useTranslation } from 'react-i18next';
import { shallowEqual, useDispatch, useSelector } from 'react-redux';
import getDefaultLocation from '../../helpers/getDefaultLocation';
import { IMG_URL } from '../../configs/app-global';
import { fetchDelivery } from '../../redux/slices/deliveries';
import { disableRefetch } from '../../redux/slices/menu';
import Loading from '../../components/loading';
import useDidUpdate from '../../helpers/useDidUpdate';
import UserData from './user-data';
import UserCard from './user-card';
import { UserOutlined } from '@ant-design/icons';
import MapCustomMarker from '../../components/map-custom-marker';
import MapErrorBoundary from '../../components/map-error-boundary';

const deliveryType = [
  { label: 'All', value: 'all', key: 1 },
  { label: 'Online', value: '1', key: 2 },
  { label: 'Offline', value: '0', key: 3 },
];

const Marker = (props) => (
  <Avatar
    src={props.url}
    icon={<UserOutlined />}
    style={{ color: '#1a3353' }}
    onClick={props.onClick}
  />
);

// Helper function to validate coordinates - declared at top to avoid hoisting issues
const isValidCoordinate = (value) => {
  const num = Number(value);
  return !isNaN(num) && isFinite(num) && num !== 0;
};

export default function DeliveriesMap() {
  const { t } = useTranslation();
  const { activeMenu } = useSelector((state) => state.menu, shallowEqual);
  const dispatch = useDispatch();
  const [active, setActive] = useState(undefined);
  const [userData, setUserData] = useState(null);
  const isMountedRef = useRef(true);
  const { settings } = useSelector(
    (state) => state.globalSettings,
    shallowEqual
  );
  const rawCenter = getDefaultLocation(settings);

  // Ensure center coordinates are always valid
  const center = {
    lat: isValidCoordinate(rawCenter?.lat) ? rawCenter.lat : -18.451436604437827,
    lng: isValidCoordinate(rawCenter?.lng) ? rawCenter.lng : -50.445899976422126,
     
  };

  const { delivery, loading } = useSelector(
    (state) => state.deliveries,
    shallowEqual
  );

  useDidUpdate(() => {
    const params = {
      page: 1,
      perPage: 100,
      online: active === 'all' ? undefined : active,
      'statuses[0]': 'new',
      'statuses[1]': 'accepted',
      'statuses[2]': 'ready',
      'statuses[3]': 'on_a_way',
    };
    dispatch(fetchDelivery(params));
  }, [active]);

  const handleLoadMap = (map, maps) => {
    try {
      if (!delivery || !Array.isArray(delivery)) {
        console.warn('DeliveriesMap: Invalid delivery data', delivery);
        return;
      }

      const markers = delivery
        .filter((item) => {
          const lat = item?.delivery_man_setting?.location?.latitude;
          const lng = item?.delivery_man_setting?.location?.longitude;
          return isValidCoordinate(lat) && isValidCoordinate(lng);
        })
        .map((item) => ({
          lat: Number(item.delivery_man_setting.location.latitude),
          lng: Number(item.delivery_man_setting.location.longitude),
        }));

      if (markers.length > 0) {
        let bounds = new maps.LatLngBounds();
        for (var i = 0; i < markers.length; i++) {
          bounds.extend(markers[i]);
        }
        map.fitBounds(bounds);
      } else {
        console.info('DeliveriesMap: No valid markers to display');
      }
    } catch (error) {
      console.error('DeliveriesMap: Error in handleLoadMap', error);
    }
  };

  const onMapClick = (e) => {
    setUserData(e);
  };

  const onCloseDeliverymanDetails = () => {
    setUserData(null);
  };

  useEffect(() => {
    if (activeMenu?.refetch && isMountedRef.current) {
      dispatch(
        fetchDelivery({
          perPage: 100,
          'statuses[0]': 'new',
          'statuses[1]': 'accepted',
          'statuses[2]': 'ready',
          'statuses[3]': 'on_a_way',
        })
      );
      dispatch(disableRefetch(activeMenu));
    }
  }, [activeMenu?.refetch]);

  // Cleanup effect
  useEffect(() => {
    return () => {
      isMountedRef.current = false;
    };
  }, []);

  return (
    <Card
      title={t('deliveries')}
      className='delivery'
      extra={
        <Select
          options={deliveryType}
          defaultValue={'all'}
          loading={loading}
          onChange={(e) => setActive(e)}
          style={{ width: '200px' }}
        />
      }
    >
      {!loading ? (
        <Row gutter={8}>
          <Col span={18}>
            <div
              className='map-container'
              style={{ height: '73vh', width: '100%' }}
            >
              {!!userData && (
                <Card className='map-user-card'>
                  <UserData
                    data={userData}
                    handleClose={onCloseDeliverymanDetails}
                  />
                </Card>
              )}
              <MapErrorBoundary>
                <MapCustomMarker center={center} handleLoadMap={handleLoadMap}>
                  {delivery && Array.isArray(delivery) ? delivery
                    .filter((item) => {
                      try {
                        const lat = item?.delivery_man_setting?.location?.latitude;
                        const lng = item?.delivery_man_setting?.location?.longitude;
                        return isValidCoordinate(lat) && isValidCoordinate(lng);
                      } catch (error) {
                        console.warn('DeliveriesMap: Error filtering delivery item', item, error);
                        return false;
                      }
                    })
                    .map((item) => {
                      try {
                        return (
                          <Marker
                            key={item.id}
                            lat={Number(item.delivery_man_setting.location.latitude)}
                            lng={Number(item.delivery_man_setting.location.longitude)}
                            url={IMG_URL + item.img}
                            onClick={() => onMapClick(item)}
                          />
                        );
                      } catch (error) {
                        console.warn('DeliveriesMap: Error rendering marker for item', item, error);
                        return null;
                      }
                    }) : []}
                </MapCustomMarker>
              </MapErrorBoundary>
            </div>
          </Col>
          <Col span={6}>
            <div className='order-list' style={{ height: '75vh' }}>
              {delivery.map((item, index) => (
                <UserCard key={item.id + index} data={item} />
              ))}
            </div>
          </Col>
        </Row>
      ) : (
        <Loading />
      )}
    </Card>
  );
}
