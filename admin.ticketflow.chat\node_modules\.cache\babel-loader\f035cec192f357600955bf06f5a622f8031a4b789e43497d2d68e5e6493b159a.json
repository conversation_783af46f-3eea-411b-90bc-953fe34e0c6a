{"ast": null, "code": "var _jsxFileName = \"C:\\\\OSPanel\\\\home\\\\admin.ticketflow.chat\\\\src\\\\components\\\\sidebar.js\",\n  _s = $RefreshSig$();\nimport React, { useMemo, useState, useRef, useEffect } from 'react';\nimport { Link, useLocation, useNavigate } from 'react-router-dom';\nimport { LogoutOutlined, MenuFoldOutlined, MenuUnfoldOutlined, SearchOutlined } from '@ant-design/icons';\nimport { Divider, Menu, Space, Layout, Modal, Input } from 'antd';\nimport { shallowEqual, useDispatch, useSelector } from 'react-redux';\nimport { addMenu, clearMenu, setMenu } from '../redux/slices/menu';\nimport { useTranslation } from 'react-i18next';\nimport LangModal from './lang-modal';\nimport getSystemIcons from '../helpers/getSystemIcons';\nimport NotificationBar from './notificationBar';\nimport { navCollapseTrigger } from '../redux/slices/theme';\nimport ThemeConfigurator from './theme-configurator';\nimport i18n from '../configs/i18next';\nimport { RiArrowDownSFill } from 'react-icons/ri';\nimport Scrollbars from 'react-custom-scrollbars';\nimport NavProfile from './nav-profile';\nimport { batch } from 'react-redux';\nimport { clearUser } from '../redux/slices/auth';\nimport { setCurrentChat } from '../redux/slices/chat';\nimport { data as allRoutes } from 'configs/menu-config';\nimport useDidUpdate from 'helpers/useDidUpdate';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst {\n  Sider\n} = Layout;\nconst Sidebar = () => {\n  _s();\n  var _languages$find;\n  const {\n    t\n  } = useTranslation();\n  const navigate = useNavigate();\n  const {\n    pathname\n  } = useLocation();\n  const {\n    user\n  } = useSelector(state => state.auth, shallowEqual);\n  const [isModalVisible, setIsModalVisible] = useState(false);\n  const isMountedRef = useRef(true);\n  const {\n    system_refund,\n    payment_type,\n    by_subscription\n  } = useSelector(state => state.globalSettings.settings, shallowEqual);\n  const {\n    navCollapsed\n  } = useSelector(state => state.theme.theme, shallowEqual);\n  const {\n    languages\n  } = useSelector(state => state.formLang, shallowEqual);\n  const dispatch = useDispatch();\n  const [langModal, setLangModal] = useState(false);\n  const {\n    myShop\n  } = useSelector(state => state.myShop, shallowEqual);\n  const {\n    theme\n  } = useSelector(state => state.theme, shallowEqual);\n  const parcelMode = useMemo(() => !!theme.parcelMode && (user === null || user === void 0 ? void 0 : user.role) === 'admin', [theme, user]);\n  const routes = useMemo(() => {\n    var _user$urls;\n    const isSubscriptionEnabled = by_subscription === '1';\n    const excludeRoutes = {\n      routes: [],\n      subRoutes: []\n    };\n    if (!isSubscriptionEnabled) {\n      excludeRoutes.subRoutes.push('subscriptions', 'my.subscriptions', 'shop.subscriptions');\n    }\n    return filterUserRoutes((_user$urls = user.urls) === null || _user$urls === void 0 ? void 0 : _user$urls.filter(item => !excludeRoutes.routes.includes(item.name)).map(item => {\n      var _item$submenu;\n      return {\n        ...item,\n        submenu: (_item$submenu = item.submenu) === null || _item$submenu === void 0 ? void 0 : _item$submenu.filter(sub => !excludeRoutes.subRoutes.includes(sub.name))\n      };\n    }));\n  }, [user]);\n  const active = routes === null || routes === void 0 ? void 0 : routes.find(item => pathname.includes(item.url));\n  const [searchTerm, setSearchTerm] = useState('');\n  const [data, setData] = useState(parcelMode ? allRoutes.parcel : routes);\n  useDidUpdate(() => {\n    if (isMountedRef.current) {\n      if (parcelMode) {\n        setData(allRoutes.parcel);\n      } else {\n        setData(routes);\n      }\n    }\n  }, [theme, user]);\n\n  // Cleanup effect\n  useEffect(() => {\n    return () => {\n      isMountedRef.current = false;\n    };\n  }, []);\n  const addNewItem = item => {\n    if (typeof item.url === 'undefined') return;\n    if (item.name === 'logout') {\n      if (isMountedRef.current) {\n        setIsModalVisible(true);\n      }\n      return;\n    }\n    const data = {\n      ...item,\n      icon: undefined,\n      children: undefined,\n      refetch: true\n    };\n    dispatch(setMenu(data));\n    navigate(`/${item.url}`);\n  };\n  function filterUserRoutes(routes) {\n    let list = routes;\n    if (myShop.type === 'shop') {\n      list = routes === null || routes === void 0 ? void 0 : routes.filter(item => (item === null || item === void 0 ? void 0 : item.name) !== 'brands');\n    }\n    if (payment_type === 'admin') {\n      list = routes === null || routes === void 0 ? void 0 : routes.filter(item => (item === null || item === void 0 ? void 0 : item.name) !== 'payments');\n    }\n    if (system_refund === '0') {\n      list = routes === null || routes === void 0 ? void 0 : routes.filter(item => (item === null || item === void 0 ? void 0 : item.name) !== 'refunds');\n    }\n    return list;\n  }\n  const menuTrigger = event => {\n    event.stopPropagation();\n    dispatch(navCollapseTrigger());\n  };\n  const addMenuItem = payload => {\n    const data = {\n      ...payload,\n      icon: undefined\n    };\n    dispatch(addMenu(data));\n  };\n  const handleOk = () => {\n    batch(() => {\n      dispatch(clearUser());\n      dispatch(clearMenu());\n      dispatch(setCurrentChat(null));\n    });\n    if (isMountedRef.current) {\n      setIsModalVisible(false);\n    }\n    localStorage.removeItem('token');\n    navigate('/login');\n  };\n  const handleCancel = () => {\n    if (isMountedRef.current) {\n      setIsModalVisible(false);\n    }\n  };\n  function getOptionList(routes) {\n    const optionTree = [];\n    routes === null || routes === void 0 ? void 0 : routes.map(item => {\n      var _item$submenu2;\n      optionTree.push(item);\n      item === null || item === void 0 ? void 0 : (_item$submenu2 = item.submenu) === null || _item$submenu2 === void 0 ? void 0 : _item$submenu2.map(sub => {\n        var _sub$children;\n        optionTree.push(sub);\n        sub === null || sub === void 0 ? void 0 : (_sub$children = sub.children) === null || _sub$children === void 0 ? void 0 : _sub$children.map(child => {\n          optionTree.push(child);\n        });\n      });\n    });\n    return optionTree;\n  }\n  const optionList = getOptionList(data);\n  const menuList = searchTerm.length > 0 ? optionList.filter(input => {\n    var _input$name;\n    return t((_input$name = input === null || input === void 0 ? void 0 : input.name) !== null && _input$name !== void 0 ? _input$name : '').toUpperCase().includes(searchTerm.toUpperCase());\n  }) : data;\n\n  // Convert menu data to Ant Design v4+ items format\n  const convertToMenuItems = menuData => {\n    if (!menuData || !Array.isArray(menuData)) {\n      return [];\n    }\n    return menuData.map((item, idx) => {\n      var _item$submenu3;\n      if (!item || !item.id) {\n        console.warn('Invalid menu item:', item);\n        return null;\n      }\n      const itemKey = item.id + '_' + idx;\n      if (((_item$submenu3 = item.submenu) === null || _item$submenu3 === void 0 ? void 0 : _item$submenu3.length) > 0) {\n        // This is a submenu with children\n        return {\n          key: itemKey,\n          label: t(item.name),\n          icon: getSystemIcons(item.icon),\n          children: item.submenu.map((submenu, idy) => {\n            var _submenu$children;\n            const submenuKey = submenu.id + '_' + idy;\n            if (((_submenu$children = submenu.children) === null || _submenu$children === void 0 ? void 0 : _submenu$children.length) > 0) {\n              var _submenu$children2;\n              // Nested submenu\n              return {\n                key: submenuKey,\n                label: /*#__PURE__*/_jsxDEV(\"span\", {\n                  onClick: () => addNewItem(submenu),\n                  children: t(submenu.name)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 209,\n                  columnNumber: 19\n                }, this),\n                icon: getSystemIcons(submenu.icon),\n                children: (_submenu$children2 = submenu.children) === null || _submenu$children2 === void 0 ? void 0 : _submenu$children2.map((sub, idk) => ({\n                  key: 'child' + idk + sub.id,\n                  label: /*#__PURE__*/_jsxDEV(Link, {\n                    to: '/' + sub.url,\n                    onClick: () => addMenuItem(sub),\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: t(sub.name)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 221,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 217,\n                    columnNumber: 21\n                  }, this),\n                  icon: getSystemIcons(sub.icon)\n                }))\n              };\n            } else {\n              // Regular submenu item\n              return {\n                key: submenu.id,\n                label: /*#__PURE__*/_jsxDEV(Link, {\n                  to: '/' + submenu.url,\n                  onClick: () => addNewItem(submenu),\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: t(submenu.name)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 236,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 232,\n                  columnNumber: 19\n                }, this),\n                icon: getSystemIcons(submenu.icon)\n              };\n            }\n          })\n        };\n      } else {\n        // Regular menu item\n        return {\n          key: item.id,\n          label: /*#__PURE__*/_jsxDEV(Link, {\n            to: '/' + item.url,\n            onClick: () => addNewItem(item),\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              children: t(item.name)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 250,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 249,\n            columnNumber: 13\n          }, this),\n          icon: getSystemIcons(item.icon)\n        };\n      }\n    });\n  };\n  const menuItems = convertToMenuItems(menuList);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(Sider, {\n      className: \"navbar-nav side-nav\",\n      width: 250,\n      collapsed: navCollapsed,\n      style: {\n        height: '100vh',\n        top: 0\n      },\n      children: [/*#__PURE__*/_jsxDEV(NavProfile, {\n        user: user\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 269,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"menu-collapse\",\n        onClick: menuTrigger,\n        children: /*#__PURE__*/_jsxDEV(MenuFoldOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 271,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 270,\n        columnNumber: 9\n      }, this), navCollapsed && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-content-center\",\n        children: /*#__PURE__*/_jsxDEV(ThemeConfigurator, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 275,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 274,\n        columnNumber: 11\n      }, this), !navCollapsed ? /*#__PURE__*/_jsxDEV(Space, {\n        className: \"mx-4 mt-2 d-flex justify-content-between\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"icon-button\",\n          onClick: () => isMountedRef.current && setLangModal(true),\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            className: \"globalOutlined\",\n            src: (_languages$find = languages.find(item => item.locale === i18n.language)) === null || _languages$find === void 0 ? void 0 : _languages$find.img,\n            alt: user.fullName\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 282,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"default-lang\",\n            children: i18n.language\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 289,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(RiArrowDownSFill, {\n            size: 15\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 290,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 281,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"d-flex\",\n          children: [/*#__PURE__*/_jsxDEV(ThemeConfigurator, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 293,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(NotificationBar, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 294,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 292,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 280,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"menu-unfold\",\n        onClick: menuTrigger,\n        children: /*#__PURE__*/_jsxDEV(MenuUnfoldOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 299,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 298,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Divider, {\n        style: {\n          margin: '10px 0'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 302,\n        columnNumber: 9\n      }, this), !navCollapsed && /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"mt-2 mb-2 d-flex justify-content-center\",\n        children: /*#__PURE__*/_jsxDEV(Input, {\n          placeholder: \"search\",\n          style: {\n            width: '90%'\n          },\n          value: searchTerm,\n          onChange: event => {\n            if (isMountedRef.current) {\n              setSearchTerm(event.target.value);\n            }\n          },\n          prefix: /*#__PURE__*/_jsxDEV(SearchOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 315,\n            columnNumber: 23\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 306,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 305,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Scrollbars, {\n        autoHeight: true,\n        autoHeightMin: window.innerHeight > 969 ? '80vh' : '77vh',\n        autoHeightMax: window.innerHeight > 969 ? '80vh' : '77vh',\n        autoHide: true,\n        children: /*#__PURE__*/_jsxDEV(Menu, {\n          theme: \"light\",\n          mode: \"inline\",\n          defaultSelectedKeys: [String(active === null || active === void 0 ? void 0 : active.id)],\n          defaultOpenKeys: !navCollapsed ? data === null || data === void 0 ? void 0 : data.map((i, idx) => i.id + '_' + idx) : [],\n          items: menuItems\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 326,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 320,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 263,\n      columnNumber: 7\n    }, this), langModal && /*#__PURE__*/_jsxDEV(LangModal, {\n      visible: langModal,\n      handleCancel: () => isMountedRef.current && setLangModal(false)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 339,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      visible: isModalVisible,\n      onOk: handleOk,\n      onCancel: handleCancel,\n      centered: true,\n      children: [/*#__PURE__*/_jsxDEV(LogoutOutlined, {\n        style: {\n          fontSize: '25px',\n          color: '#08c'\n        },\n        theme: \"primary\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 351,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"ml-2\",\n        children: t('leave.site')\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 355,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 345,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(Sidebar, \"Ro7bFrvvh2p9QNKd54pNb/YlXVQ=\", false, function () {\n  return [useTranslation, useNavigate, useLocation, useSelector, useSelector, useSelector, useSelector, useDispatch, useSelector, useSelector, useDidUpdate];\n});\n_c = Sidebar;\nexport default Sidebar;\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");", "map": {"version": 3, "names": ["React", "useMemo", "useState", "useRef", "useEffect", "Link", "useLocation", "useNavigate", "LogoutOutlined", "MenuFoldOutlined", "MenuUnfoldOutlined", "SearchOutlined", "Divider", "<PERSON><PERSON>", "Space", "Layout", "Modal", "Input", "shallowEqual", "useDispatch", "useSelector", "addMenu", "clearMenu", "setMenu", "useTranslation", "LangModal", "getSystemIcons", "NotificationBar", "navCollapseTrigger", "ThemeConfigurator", "i18n", "RiArrowDownSFill", "Scrollbars", "NavProfile", "batch", "clearUser", "setCurrentChat", "data", "allRoutes", "useDidUpdate", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "<PERSON><PERSON>", "Sidebar", "_s", "_languages$find", "t", "navigate", "pathname", "user", "state", "auth", "isModalVisible", "setIsModalVisible", "isMountedRef", "system_refund", "payment_type", "by_subscription", "globalSettings", "settings", "navCollapsed", "theme", "languages", "formLang", "dispatch", "langModal", "setLangModal", "myShop", "parcelMode", "role", "routes", "_user$urls", "isSubscriptionEnabled", "excludeRoutes", "subRoutes", "push", "filterUserRoutes", "urls", "filter", "item", "includes", "name", "map", "_item$submenu", "submenu", "sub", "active", "find", "url", "searchTerm", "setSearchTerm", "setData", "parcel", "current", "addNewItem", "icon", "undefined", "children", "refetch", "list", "type", "menuTrigger", "event", "stopPropagation", "addMenuItem", "payload", "handleOk", "localStorage", "removeItem", "handleCancel", "getOptionList", "optionTree", "_item$submenu2", "_sub$children", "child", "optionList", "menuList", "length", "input", "_input$name", "toUpperCase", "convertToMenuItems", "menuData", "Array", "isArray", "idx", "_item$submenu3", "id", "console", "warn", "itemKey", "key", "label", "idy", "_submenu$children", "submenuKey", "_submenu$children2", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "idk", "to", "menuItems", "className", "width", "collapsed", "style", "height", "top", "src", "locale", "language", "img", "alt", "fullName", "size", "margin", "placeholder", "value", "onChange", "target", "prefix", "autoHeight", "autoHeightMin", "window", "innerHeight", "autoHeightMax", "autoHide", "mode", "defaultSelectedKeys", "String", "defaultOpenKeys", "i", "items", "visible", "onOk", "onCancel", "centered", "fontSize", "color", "_c", "$RefreshReg$"], "sources": ["C:/OSPanel/home/<USER>/src/components/sidebar.js"], "sourcesContent": ["import React, { useMemo, useState, useRef, useEffect } from 'react';\nimport { Link, useLocation, useNavigate } from 'react-router-dom';\nimport {\n  LogoutOutlined,\n  MenuFoldOutlined,\n  MenuUnfoldOutlined,\n  SearchOutlined,\n} from '@ant-design/icons';\nimport { Divider, Menu, Space, Layout, Modal, Input } from 'antd';\nimport { shallowEqual, useDispatch, useSelector } from 'react-redux';\nimport { addMenu, clearMenu, setMenu } from '../redux/slices/menu';\nimport { useTranslation } from 'react-i18next';\nimport LangModal from './lang-modal';\nimport getSystemIcons from '../helpers/getSystemIcons';\nimport NotificationBar from './notificationBar';\nimport { navCollapseTrigger } from '../redux/slices/theme';\nimport ThemeConfigurator from './theme-configurator';\nimport i18n from '../configs/i18next';\nimport { RiArrowDownSFill } from 'react-icons/ri';\nimport Scrollbars from 'react-custom-scrollbars';\nimport NavProfile from './nav-profile';\nimport { batch } from 'react-redux';\nimport { clearUser } from '../redux/slices/auth';\nimport { setCurrentChat } from '../redux/slices/chat';\nimport { data as allRoutes } from 'configs/menu-config';\nimport useDidUpdate from 'helpers/useDidUpdate';\nconst { Sider } = Layout;\n\nconst Sidebar = () => {\n  const { t } = useTranslation();\n  const navigate = useNavigate();\n  const { pathname } = useLocation();\n  const { user } = useSelector((state) => state.auth, shallowEqual);\n  const [isModalVisible, setIsModalVisible] = useState(false);\n  const isMountedRef = useRef(true);\n  const { system_refund, payment_type, by_subscription } = useSelector(\n    (state) => state.globalSettings.settings,\n    shallowEqual,\n  );\n  const { navCollapsed } = useSelector(\n    (state) => state.theme.theme,\n    shallowEqual,\n  );\n  const { languages } = useSelector((state) => state.formLang, shallowEqual);\n  const dispatch = useDispatch();\n  const [langModal, setLangModal] = useState(false);\n  const { myShop } = useSelector((state) => state.myShop, shallowEqual);\n  const { theme } = useSelector((state) => state.theme, shallowEqual);\n  const parcelMode = useMemo(\n    () => !!theme.parcelMode && user?.role === 'admin',\n    [theme, user],\n  );\n  const routes = useMemo(() => {\n    const isSubscriptionEnabled = by_subscription === '1';\n    const excludeRoutes = { routes: [], subRoutes: [] };\n    if (!isSubscriptionEnabled) {\n      excludeRoutes.subRoutes.push(\n        'subscriptions',\n        'my.subscriptions',\n        'shop.subscriptions',\n      );\n    }\n    return filterUserRoutes(\n      user.urls\n        ?.filter((item) => !excludeRoutes.routes.includes(item.name))\n        .map((item) => ({\n          ...item,\n          submenu: item.submenu?.filter(\n            (sub) => !excludeRoutes.subRoutes.includes(sub.name),\n          ),\n        })),\n    );\n  }, [user]);\n  const active = routes?.find((item) => pathname.includes(item.url));\n  const [searchTerm, setSearchTerm] = useState('');\n  const [data, setData] = useState(parcelMode ? allRoutes.parcel : routes);\n\n  useDidUpdate(() => {\n    if (isMountedRef.current) {\n      if (parcelMode) {\n        setData(allRoutes.parcel);\n      } else {\n        setData(routes);\n      }\n    }\n  }, [theme, user]);\n\n  // Cleanup effect\n  useEffect(() => {\n    return () => {\n      isMountedRef.current = false;\n    };\n  }, []);\n\n  const addNewItem = (item) => {\n    if (typeof item.url === 'undefined') return;\n    if (item.name === 'logout') {\n      if (isMountedRef.current) {\n        setIsModalVisible(true);\n      }\n      return;\n    }\n    const data = {\n      ...item,\n      icon: undefined,\n      children: undefined,\n      refetch: true,\n    };\n    dispatch(setMenu(data));\n    navigate(`/${item.url}`);\n  };\n\n  function filterUserRoutes(routes) {\n    let list = routes;\n    if (myShop.type === 'shop') {\n      list = routes?.filter((item) => item?.name !== 'brands');\n    }\n    if (payment_type === 'admin') {\n      list = routes?.filter((item) => item?.name !== 'payments');\n    }\n    if (system_refund === '0') {\n      list = routes?.filter((item) => item?.name !== 'refunds');\n    }\n    return list;\n  }\n\n  const menuTrigger = (event) => {\n    event.stopPropagation();\n    dispatch(navCollapseTrigger());\n  };\n\n  const addMenuItem = (payload) => {\n    const data = { ...payload, icon: undefined };\n    dispatch(addMenu(data));\n  };\n\n  const handleOk = () => {\n    batch(() => {\n      dispatch(clearUser());\n      dispatch(clearMenu());\n      dispatch(setCurrentChat(null));\n    });\n    if (isMountedRef.current) {\n      setIsModalVisible(false);\n    }\n    localStorage.removeItem('token');\n    navigate('/login');\n  };\n\n  const handleCancel = () => {\n    if (isMountedRef.current) {\n      setIsModalVisible(false);\n    }\n  };\n\n  function getOptionList(routes) {\n    const optionTree = [];\n    routes?.map((item) => {\n      optionTree.push(item);\n      item?.submenu?.map((sub) => {\n        optionTree.push(sub);\n        sub?.children?.map((child) => {\n          optionTree.push(child);\n        });\n      });\n    });\n    return optionTree;\n  }\n\n  const optionList = getOptionList(data);\n\n  const menuList =\n    searchTerm.length > 0\n      ? optionList.filter((input) =>\n          t(input?.name ?? '')\n            .toUpperCase()\n            .includes(searchTerm.toUpperCase()),\n        )\n      : data;\n\n  // Convert menu data to Ant Design v4+ items format\n  const convertToMenuItems = (menuData) => {\n    if (!menuData || !Array.isArray(menuData)) {\n      return [];\n    }\n\n    return menuData.map((item, idx) => {\n      if (!item || !item.id) {\n        console.warn('Invalid menu item:', item);\n        return null;\n      }\n\n      const itemKey = item.id + '_' + idx;\n\n      if (item.submenu?.length > 0) {\n        // This is a submenu with children\n        return {\n          key: itemKey,\n          label: t(item.name),\n          icon: getSystemIcons(item.icon),\n          children: item.submenu.map((submenu, idy) => {\n            const submenuKey = submenu.id + '_' + idy;\n\n            if (submenu.children?.length > 0) {\n              // Nested submenu\n              return {\n                key: submenuKey,\n                label: (\n                  <span onClick={() => addNewItem(submenu)}>\n                    {t(submenu.name)}\n                  </span>\n                ),\n                icon: getSystemIcons(submenu.icon),\n                children: submenu.children?.map((sub, idk) => ({\n                  key: 'child' + idk + sub.id,\n                  label: (\n                    <Link\n                      to={'/' + sub.url}\n                      onClick={() => addMenuItem(sub)}\n                    >\n                      <span>{t(sub.name)}</span>\n                    </Link>\n                  ),\n                  icon: getSystemIcons(sub.icon),\n                })),\n              };\n            } else {\n              // Regular submenu item\n              return {\n                key: submenu.id,\n                label: (\n                  <Link\n                    to={'/' + submenu.url}\n                    onClick={() => addNewItem(submenu)}\n                  >\n                    <span>{t(submenu.name)}</span>\n                  </Link>\n                ),\n                icon: getSystemIcons(submenu.icon),\n              };\n            }\n          }),\n        };\n      } else {\n        // Regular menu item\n        return {\n          key: item.id,\n          label: (\n            <Link to={'/' + item.url} onClick={() => addNewItem(item)}>\n              <span>{t(item.name)}</span>\n            </Link>\n          ),\n          icon: getSystemIcons(item.icon),\n        };\n      }\n    });\n  };\n\n  const menuItems = convertToMenuItems(menuList);\n\n  return (\n    <>\n      <Sider\n        className='navbar-nav side-nav'\n        width={250}\n        collapsed={navCollapsed}\n        style={{ height: '100vh', top: 0 }}\n      >\n        <NavProfile user={user} />\n        <div className='menu-collapse' onClick={menuTrigger}>\n          <MenuFoldOutlined />\n        </div>\n        {navCollapsed && (\n          <div className='flex justify-content-center'>\n            <ThemeConfigurator />\n          </div>\n        )}\n\n        {!navCollapsed ? (\n          <Space className='mx-4 mt-2 d-flex justify-content-between'>\n            <span className='icon-button' onClick={() => isMountedRef.current && setLangModal(true)}>\n              <img\n                className='globalOutlined'\n                src={\n                  languages.find((item) => item.locale === i18n.language)?.img\n                }\n                alt={user.fullName}\n              />\n              <span className='default-lang'>{i18n.language}</span>\n              <RiArrowDownSFill size={15} />\n            </span>\n            <span className='d-flex'>\n              <ThemeConfigurator />\n              <NotificationBar />\n            </span>\n          </Space>\n        ) : (\n          <div className='menu-unfold' onClick={menuTrigger}>\n            <MenuUnfoldOutlined />\n          </div>\n        )}\n        <Divider style={{ margin: '10px 0' }} />\n\n        {!navCollapsed && (\n          <span className='mt-2 mb-2 d-flex justify-content-center'>\n            <Input\n              placeholder='search'\n              style={{ width: '90%' }}\n              value={searchTerm}\n              onChange={(event) => {\n                if (isMountedRef.current) {\n                  setSearchTerm(event.target.value);\n                }\n              }}\n              prefix={<SearchOutlined />}\n            />\n          </span>\n        )}\n\n        <Scrollbars\n          autoHeight\n          autoHeightMin={window.innerHeight > 969 ? '80vh' : '77vh'}\n          autoHeightMax={window.innerHeight > 969 ? '80vh' : '77vh'}\n          autoHide\n        >\n          <Menu\n            theme='light'\n            mode='inline'\n            defaultSelectedKeys={[String(active?.id)]}\n            defaultOpenKeys={\n              !navCollapsed ? data?.map((i, idx) => i.id + '_' + idx) : []\n            }\n            items={menuItems}\n          />\n        </Scrollbars>\n      </Sider>\n\n      {langModal && (\n        <LangModal\n          visible={langModal}\n          handleCancel={() => isMountedRef.current && setLangModal(false)}\n        />\n      )}\n\n      <Modal\n        visible={isModalVisible}\n        onOk={handleOk}\n        onCancel={handleCancel}\n        centered\n      >\n        <LogoutOutlined\n          style={{ fontSize: '25px', color: '#08c' }}\n          theme='primary'\n        />\n        <span className='ml-2'>{t('leave.site')}</span>\n      </Modal>\n    </>\n  );\n};\nexport default Sidebar;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,OAAO,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AACnE,SAASC,IAAI,EAAEC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AACjE,SACEC,cAAc,EACdC,gBAAgB,EAChBC,kBAAkB,EAClBC,cAAc,QACT,mBAAmB;AAC1B,SAASC,OAAO,EAAEC,IAAI,EAAEC,KAAK,EAAEC,MAAM,EAAEC,KAAK,EAAEC,KAAK,QAAQ,MAAM;AACjE,SAASC,YAAY,EAAEC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACpE,SAASC,OAAO,EAAEC,SAAS,EAAEC,OAAO,QAAQ,sBAAsB;AAClE,SAASC,cAAc,QAAQ,eAAe;AAC9C,OAAOC,SAAS,MAAM,cAAc;AACpC,OAAOC,cAAc,MAAM,2BAA2B;AACtD,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,SAASC,kBAAkB,QAAQ,uBAAuB;AAC1D,OAAOC,iBAAiB,MAAM,sBAAsB;AACpD,OAAOC,IAAI,MAAM,oBAAoB;AACrC,SAASC,gBAAgB,QAAQ,gBAAgB;AACjD,OAAOC,UAAU,MAAM,yBAAyB;AAChD,OAAOC,UAAU,MAAM,eAAe;AACtC,SAASC,KAAK,QAAQ,aAAa;AACnC,SAASC,SAAS,QAAQ,sBAAsB;AAChD,SAASC,cAAc,QAAQ,sBAAsB;AACrD,SAASC,IAAI,IAAIC,SAAS,QAAQ,qBAAqB;AACvD,OAAOC,YAAY,MAAM,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAChD,MAAM;EAAEC;AAAM,CAAC,GAAG7B,MAAM;AAExB,MAAM8B,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,eAAA;EACpB,MAAM;IAAEC;EAAE,CAAC,GAAGxB,cAAc,CAAC,CAAC;EAC9B,MAAMyB,QAAQ,GAAG1C,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAE2C;EAAS,CAAC,GAAG5C,WAAW,CAAC,CAAC;EAClC,MAAM;IAAE6C;EAAK,CAAC,GAAG/B,WAAW,CAAEgC,KAAK,IAAKA,KAAK,CAACC,IAAI,EAAEnC,YAAY,CAAC;EACjE,MAAM,CAACoC,cAAc,EAAEC,iBAAiB,CAAC,GAAGrD,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAMsD,YAAY,GAAGrD,MAAM,CAAC,IAAI,CAAC;EACjC,MAAM;IAAEsD,aAAa;IAAEC,YAAY;IAAEC;EAAgB,CAAC,GAAGvC,WAAW,CACjEgC,KAAK,IAAKA,KAAK,CAACQ,cAAc,CAACC,QAAQ,EACxC3C,YACF,CAAC;EACD,MAAM;IAAE4C;EAAa,CAAC,GAAG1C,WAAW,CACjCgC,KAAK,IAAKA,KAAK,CAACW,KAAK,CAACA,KAAK,EAC5B7C,YACF,CAAC;EACD,MAAM;IAAE8C;EAAU,CAAC,GAAG5C,WAAW,CAAEgC,KAAK,IAAKA,KAAK,CAACa,QAAQ,EAAE/C,YAAY,CAAC;EAC1E,MAAMgD,QAAQ,GAAG/C,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACgD,SAAS,EAAEC,YAAY,CAAC,GAAGlE,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM;IAAEmE;EAAO,CAAC,GAAGjD,WAAW,CAAEgC,KAAK,IAAKA,KAAK,CAACiB,MAAM,EAAEnD,YAAY,CAAC;EACrE,MAAM;IAAE6C;EAAM,CAAC,GAAG3C,WAAW,CAAEgC,KAAK,IAAKA,KAAK,CAACW,KAAK,EAAE7C,YAAY,CAAC;EACnE,MAAMoD,UAAU,GAAGrE,OAAO,CACxB,MAAM,CAAC,CAAC8D,KAAK,CAACO,UAAU,IAAI,CAAAnB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoB,IAAI,MAAK,OAAO,EAClD,CAACR,KAAK,EAAEZ,IAAI,CACd,CAAC;EACD,MAAMqB,MAAM,GAAGvE,OAAO,CAAC,MAAM;IAAA,IAAAwE,UAAA;IAC3B,MAAMC,qBAAqB,GAAGf,eAAe,KAAK,GAAG;IACrD,MAAMgB,aAAa,GAAG;MAAEH,MAAM,EAAE,EAAE;MAAEI,SAAS,EAAE;IAAG,CAAC;IACnD,IAAI,CAACF,qBAAqB,EAAE;MAC1BC,aAAa,CAACC,SAAS,CAACC,IAAI,CAC1B,eAAe,EACf,kBAAkB,EAClB,oBACF,CAAC;IACH;IACA,OAAOC,gBAAgB,EAAAL,UAAA,GACrBtB,IAAI,CAAC4B,IAAI,cAAAN,UAAA,uBAATA,UAAA,CACIO,MAAM,CAAEC,IAAI,IAAK,CAACN,aAAa,CAACH,MAAM,CAACU,QAAQ,CAACD,IAAI,CAACE,IAAI,CAAC,CAAC,CAC5DC,GAAG,CAAEH,IAAI;MAAA,IAAAI,aAAA;MAAA,OAAM;QACd,GAAGJ,IAAI;QACPK,OAAO,GAAAD,aAAA,GAAEJ,IAAI,CAACK,OAAO,cAAAD,aAAA,uBAAZA,aAAA,CAAcL,MAAM,CAC1BO,GAAG,IAAK,CAACZ,aAAa,CAACC,SAAS,CAACM,QAAQ,CAACK,GAAG,CAACJ,IAAI,CACrD;MACF,CAAC;IAAA,CAAC,CACN,CAAC;EACH,CAAC,EAAE,CAAChC,IAAI,CAAC,CAAC;EACV,MAAMqC,MAAM,GAAGhB,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEiB,IAAI,CAAER,IAAI,IAAK/B,QAAQ,CAACgC,QAAQ,CAACD,IAAI,CAACS,GAAG,CAAC,CAAC;EAClE,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAG1F,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACmC,IAAI,EAAEwD,OAAO,CAAC,GAAG3F,QAAQ,CAACoE,UAAU,GAAGhC,SAAS,CAACwD,MAAM,GAAGtB,MAAM,CAAC;EAExEjC,YAAY,CAAC,MAAM;IACjB,IAAIiB,YAAY,CAACuC,OAAO,EAAE;MACxB,IAAIzB,UAAU,EAAE;QACduB,OAAO,CAACvD,SAAS,CAACwD,MAAM,CAAC;MAC3B,CAAC,MAAM;QACLD,OAAO,CAACrB,MAAM,CAAC;MACjB;IACF;EACF,CAAC,EAAE,CAACT,KAAK,EAAEZ,IAAI,CAAC,CAAC;;EAEjB;EACA/C,SAAS,CAAC,MAAM;IACd,OAAO,MAAM;MACXoD,YAAY,CAACuC,OAAO,GAAG,KAAK;IAC9B,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,UAAU,GAAIf,IAAI,IAAK;IAC3B,IAAI,OAAOA,IAAI,CAACS,GAAG,KAAK,WAAW,EAAE;IACrC,IAAIT,IAAI,CAACE,IAAI,KAAK,QAAQ,EAAE;MAC1B,IAAI3B,YAAY,CAACuC,OAAO,EAAE;QACxBxC,iBAAiB,CAAC,IAAI,CAAC;MACzB;MACA;IACF;IACA,MAAMlB,IAAI,GAAG;MACX,GAAG4C,IAAI;MACPgB,IAAI,EAAEC,SAAS;MACfC,QAAQ,EAAED,SAAS;MACnBE,OAAO,EAAE;IACX,CAAC;IACDlC,QAAQ,CAAC3C,OAAO,CAACc,IAAI,CAAC,CAAC;IACvBY,QAAQ,CAAE,IAAGgC,IAAI,CAACS,GAAI,EAAC,CAAC;EAC1B,CAAC;EAED,SAASZ,gBAAgBA,CAACN,MAAM,EAAE;IAChC,IAAI6B,IAAI,GAAG7B,MAAM;IACjB,IAAIH,MAAM,CAACiC,IAAI,KAAK,MAAM,EAAE;MAC1BD,IAAI,GAAG7B,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEQ,MAAM,CAAEC,IAAI,IAAK,CAAAA,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEE,IAAI,MAAK,QAAQ,CAAC;IAC1D;IACA,IAAIzB,YAAY,KAAK,OAAO,EAAE;MAC5B2C,IAAI,GAAG7B,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEQ,MAAM,CAAEC,IAAI,IAAK,CAAAA,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEE,IAAI,MAAK,UAAU,CAAC;IAC5D;IACA,IAAI1B,aAAa,KAAK,GAAG,EAAE;MACzB4C,IAAI,GAAG7B,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEQ,MAAM,CAAEC,IAAI,IAAK,CAAAA,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEE,IAAI,MAAK,SAAS,CAAC;IAC3D;IACA,OAAOkB,IAAI;EACb;EAEA,MAAME,WAAW,GAAIC,KAAK,IAAK;IAC7BA,KAAK,CAACC,eAAe,CAAC,CAAC;IACvBvC,QAAQ,CAACtC,kBAAkB,CAAC,CAAC,CAAC;EAChC,CAAC;EAED,MAAM8E,WAAW,GAAIC,OAAO,IAAK;IAC/B,MAAMtE,IAAI,GAAG;MAAE,GAAGsE,OAAO;MAAEV,IAAI,EAAEC;IAAU,CAAC;IAC5ChC,QAAQ,CAAC7C,OAAO,CAACgB,IAAI,CAAC,CAAC;EACzB,CAAC;EAED,MAAMuE,QAAQ,GAAGA,CAAA,KAAM;IACrB1E,KAAK,CAAC,MAAM;MACVgC,QAAQ,CAAC/B,SAAS,CAAC,CAAC,CAAC;MACrB+B,QAAQ,CAAC5C,SAAS,CAAC,CAAC,CAAC;MACrB4C,QAAQ,CAAC9B,cAAc,CAAC,IAAI,CAAC,CAAC;IAChC,CAAC,CAAC;IACF,IAAIoB,YAAY,CAACuC,OAAO,EAAE;MACxBxC,iBAAiB,CAAC,KAAK,CAAC;IAC1B;IACAsD,YAAY,CAACC,UAAU,CAAC,OAAO,CAAC;IAChC7D,QAAQ,CAAC,QAAQ,CAAC;EACpB,CAAC;EAED,MAAM8D,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAIvD,YAAY,CAACuC,OAAO,EAAE;MACxBxC,iBAAiB,CAAC,KAAK,CAAC;IAC1B;EACF,CAAC;EAED,SAASyD,aAAaA,CAACxC,MAAM,EAAE;IAC7B,MAAMyC,UAAU,GAAG,EAAE;IACrBzC,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEY,GAAG,CAAEH,IAAI,IAAK;MAAA,IAAAiC,cAAA;MACpBD,UAAU,CAACpC,IAAI,CAACI,IAAI,CAAC;MACrBA,IAAI,aAAJA,IAAI,wBAAAiC,cAAA,GAAJjC,IAAI,CAAEK,OAAO,cAAA4B,cAAA,uBAAbA,cAAA,CAAe9B,GAAG,CAAEG,GAAG,IAAK;QAAA,IAAA4B,aAAA;QAC1BF,UAAU,CAACpC,IAAI,CAACU,GAAG,CAAC;QACpBA,GAAG,aAAHA,GAAG,wBAAA4B,aAAA,GAAH5B,GAAG,CAAEY,QAAQ,cAAAgB,aAAA,uBAAbA,aAAA,CAAe/B,GAAG,CAAEgC,KAAK,IAAK;UAC5BH,UAAU,CAACpC,IAAI,CAACuC,KAAK,CAAC;QACxB,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,OAAOH,UAAU;EACnB;EAEA,MAAMI,UAAU,GAAGL,aAAa,CAAC3E,IAAI,CAAC;EAEtC,MAAMiF,QAAQ,GACZ3B,UAAU,CAAC4B,MAAM,GAAG,CAAC,GACjBF,UAAU,CAACrC,MAAM,CAAEwC,KAAK;IAAA,IAAAC,WAAA;IAAA,OACtBzE,CAAC,EAAAyE,WAAA,GAACD,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAErC,IAAI,cAAAsC,WAAA,cAAAA,WAAA,GAAI,EAAE,CAAC,CACjBC,WAAW,CAAC,CAAC,CACbxC,QAAQ,CAACS,UAAU,CAAC+B,WAAW,CAAC,CAAC,CAAC;EAAA,CACvC,CAAC,GACDrF,IAAI;;EAEV;EACA,MAAMsF,kBAAkB,GAAIC,QAAQ,IAAK;IACvC,IAAI,CAACA,QAAQ,IAAI,CAACC,KAAK,CAACC,OAAO,CAACF,QAAQ,CAAC,EAAE;MACzC,OAAO,EAAE;IACX;IAEA,OAAOA,QAAQ,CAACxC,GAAG,CAAC,CAACH,IAAI,EAAE8C,GAAG,KAAK;MAAA,IAAAC,cAAA;MACjC,IAAI,CAAC/C,IAAI,IAAI,CAACA,IAAI,CAACgD,EAAE,EAAE;QACrBC,OAAO,CAACC,IAAI,CAAC,oBAAoB,EAAElD,IAAI,CAAC;QACxC,OAAO,IAAI;MACb;MAEA,MAAMmD,OAAO,GAAGnD,IAAI,CAACgD,EAAE,GAAG,GAAG,GAAGF,GAAG;MAEnC,IAAI,EAAAC,cAAA,GAAA/C,IAAI,CAACK,OAAO,cAAA0C,cAAA,uBAAZA,cAAA,CAAcT,MAAM,IAAG,CAAC,EAAE;QAC5B;QACA,OAAO;UACLc,GAAG,EAAED,OAAO;UACZE,KAAK,EAAEtF,CAAC,CAACiC,IAAI,CAACE,IAAI,CAAC;UACnBc,IAAI,EAAEvE,cAAc,CAACuD,IAAI,CAACgB,IAAI,CAAC;UAC/BE,QAAQ,EAAElB,IAAI,CAACK,OAAO,CAACF,GAAG,CAAC,CAACE,OAAO,EAAEiD,GAAG,KAAK;YAAA,IAAAC,iBAAA;YAC3C,MAAMC,UAAU,GAAGnD,OAAO,CAAC2C,EAAE,GAAG,GAAG,GAAGM,GAAG;YAEzC,IAAI,EAAAC,iBAAA,GAAAlD,OAAO,CAACa,QAAQ,cAAAqC,iBAAA,uBAAhBA,iBAAA,CAAkBjB,MAAM,IAAG,CAAC,EAAE;cAAA,IAAAmB,kBAAA;cAChC;cACA,OAAO;gBACLL,GAAG,EAAEI,UAAU;gBACfH,KAAK,eACH7F,OAAA;kBAAMkG,OAAO,EAAEA,CAAA,KAAM3C,UAAU,CAACV,OAAO,CAAE;kBAAAa,QAAA,EACtCnD,CAAC,CAACsC,OAAO,CAACH,IAAI;gBAAC;kBAAAyD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACZ,CACP;gBACD9C,IAAI,EAAEvE,cAAc,CAAC4D,OAAO,CAACW,IAAI,CAAC;gBAClCE,QAAQ,GAAAuC,kBAAA,GAAEpD,OAAO,CAACa,QAAQ,cAAAuC,kBAAA,uBAAhBA,kBAAA,CAAkBtD,GAAG,CAAC,CAACG,GAAG,EAAEyD,GAAG,MAAM;kBAC7CX,GAAG,EAAE,OAAO,GAAGW,GAAG,GAAGzD,GAAG,CAAC0C,EAAE;kBAC3BK,KAAK,eACH7F,OAAA,CAACpC,IAAI;oBACH4I,EAAE,EAAE,GAAG,GAAG1D,GAAG,CAACG,GAAI;oBAClBiD,OAAO,EAAEA,CAAA,KAAMjC,WAAW,CAACnB,GAAG,CAAE;oBAAAY,QAAA,eAEhC1D,OAAA;sBAAA0D,QAAA,EAAOnD,CAAC,CAACuC,GAAG,CAACJ,IAAI;oBAAC;sBAAAyD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtB,CACP;kBACD9C,IAAI,EAAEvE,cAAc,CAAC6D,GAAG,CAACU,IAAI;gBAC/B,CAAC,CAAC;cACJ,CAAC;YACH,CAAC,MAAM;cACL;cACA,OAAO;gBACLoC,GAAG,EAAE/C,OAAO,CAAC2C,EAAE;gBACfK,KAAK,eACH7F,OAAA,CAACpC,IAAI;kBACH4I,EAAE,EAAE,GAAG,GAAG3D,OAAO,CAACI,GAAI;kBACtBiD,OAAO,EAAEA,CAAA,KAAM3C,UAAU,CAACV,OAAO,CAAE;kBAAAa,QAAA,eAEnC1D,OAAA;oBAAA0D,QAAA,EAAOnD,CAAC,CAACsC,OAAO,CAACH,IAAI;kBAAC;oBAAAyD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B,CACP;gBACD9C,IAAI,EAAEvE,cAAc,CAAC4D,OAAO,CAACW,IAAI;cACnC,CAAC;YACH;UACF,CAAC;QACH,CAAC;MACH,CAAC,MAAM;QACL;QACA,OAAO;UACLoC,GAAG,EAAEpD,IAAI,CAACgD,EAAE;UACZK,KAAK,eACH7F,OAAA,CAACpC,IAAI;YAAC4I,EAAE,EAAE,GAAG,GAAGhE,IAAI,CAACS,GAAI;YAACiD,OAAO,EAAEA,CAAA,KAAM3C,UAAU,CAACf,IAAI,CAAE;YAAAkB,QAAA,eACxD1D,OAAA;cAAA0D,QAAA,EAAOnD,CAAC,CAACiC,IAAI,CAACE,IAAI;YAAC;cAAAyD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvB,CACP;UACD9C,IAAI,EAAEvE,cAAc,CAACuD,IAAI,CAACgB,IAAI;QAChC,CAAC;MACH;IACF,CAAC,CAAC;EACJ,CAAC;EAED,MAAMiD,SAAS,GAAGvB,kBAAkB,CAACL,QAAQ,CAAC;EAE9C,oBACE7E,OAAA,CAAAE,SAAA;IAAAwD,QAAA,gBACE1D,OAAA,CAACG,KAAK;MACJuG,SAAS,EAAC,qBAAqB;MAC/BC,KAAK,EAAE,GAAI;MACXC,SAAS,EAAEvF,YAAa;MACxBwF,KAAK,EAAE;QAAEC,MAAM,EAAE,OAAO;QAAEC,GAAG,EAAE;MAAE,CAAE;MAAArD,QAAA,gBAEnC1D,OAAA,CAACR,UAAU;QAACkB,IAAI,EAAEA;MAAK;QAAAyF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC1BtG,OAAA;QAAK0G,SAAS,EAAC,eAAe;QAACR,OAAO,EAAEpC,WAAY;QAAAJ,QAAA,eAClD1D,OAAA,CAAChC,gBAAgB;UAAAmI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CAAC,EACLjF,YAAY,iBACXrB,OAAA;QAAK0G,SAAS,EAAC,6BAA6B;QAAAhD,QAAA,eAC1C1D,OAAA,CAACZ,iBAAiB;UAAA+G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClB,CACN,EAEA,CAACjF,YAAY,gBACZrB,OAAA,CAAC3B,KAAK;QAACqI,SAAS,EAAC,0CAA0C;QAAAhD,QAAA,gBACzD1D,OAAA;UAAM0G,SAAS,EAAC,aAAa;UAACR,OAAO,EAAEA,CAAA,KAAMnF,YAAY,CAACuC,OAAO,IAAI3B,YAAY,CAAC,IAAI,CAAE;UAAA+B,QAAA,gBACtF1D,OAAA;YACE0G,SAAS,EAAC,gBAAgB;YAC1BM,GAAG,GAAA1G,eAAA,GACDiB,SAAS,CAACyB,IAAI,CAAER,IAAI,IAAKA,IAAI,CAACyE,MAAM,KAAK5H,IAAI,CAAC6H,QAAQ,CAAC,cAAA5G,eAAA,uBAAvDA,eAAA,CAAyD6G,GAC1D;YACDC,GAAG,EAAE1G,IAAI,CAAC2G;UAAS;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpB,CAAC,eACFtG,OAAA;YAAM0G,SAAS,EAAC,cAAc;YAAAhD,QAAA,EAAErE,IAAI,CAAC6H;UAAQ;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACrDtG,OAAA,CAACV,gBAAgB;YAACgI,IAAI,EAAE;UAAG;YAAAnB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC,eACPtG,OAAA;UAAM0G,SAAS,EAAC,QAAQ;UAAAhD,QAAA,gBACtB1D,OAAA,CAACZ,iBAAiB;YAAA+G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACrBtG,OAAA,CAACd,eAAe;YAAAiH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,gBAERtG,OAAA;QAAK0G,SAAS,EAAC,aAAa;QAACR,OAAO,EAAEpC,WAAY;QAAAJ,QAAA,eAChD1D,OAAA,CAAC/B,kBAAkB;UAAAkI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB,CACN,eACDtG,OAAA,CAAC7B,OAAO;QAAC0I,KAAK,EAAE;UAAEU,MAAM,EAAE;QAAS;MAAE;QAAApB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EAEvC,CAACjF,YAAY,iBACZrB,OAAA;QAAM0G,SAAS,EAAC,yCAAyC;QAAAhD,QAAA,eACvD1D,OAAA,CAACxB,KAAK;UACJgJ,WAAW,EAAC,QAAQ;UACpBX,KAAK,EAAE;YAAEF,KAAK,EAAE;UAAM,CAAE;UACxBc,KAAK,EAAEvE,UAAW;UAClBwE,QAAQ,EAAG3D,KAAK,IAAK;YACnB,IAAIhD,YAAY,CAACuC,OAAO,EAAE;cACxBH,aAAa,CAACY,KAAK,CAAC4D,MAAM,CAACF,KAAK,CAAC;YACnC;UACF,CAAE;UACFG,MAAM,eAAE5H,OAAA,CAAC9B,cAAc;YAAAiI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACP,eAEDtG,OAAA,CAACT,UAAU;QACTsI,UAAU;QACVC,aAAa,EAAEC,MAAM,CAACC,WAAW,GAAG,GAAG,GAAG,MAAM,GAAG,MAAO;QAC1DC,aAAa,EAAEF,MAAM,CAACC,WAAW,GAAG,GAAG,GAAG,MAAM,GAAG,MAAO;QAC1DE,QAAQ;QAAAxE,QAAA,eAER1D,OAAA,CAAC5B,IAAI;UACHkD,KAAK,EAAC,OAAO;UACb6G,IAAI,EAAC,QAAQ;UACbC,mBAAmB,EAAE,CAACC,MAAM,CAACtF,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEyC,EAAE,CAAC,CAAE;UAC1C8C,eAAe,EACb,CAACjH,YAAY,GAAGzB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE+C,GAAG,CAAC,CAAC4F,CAAC,EAAEjD,GAAG,KAAKiD,CAAC,CAAC/C,EAAE,GAAG,GAAG,GAAGF,GAAG,CAAC,GAAG,EAC3D;UACDkD,KAAK,EAAE/B;QAAU;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC,EAEP5E,SAAS,iBACR1B,OAAA,CAAChB,SAAS;MACRyJ,OAAO,EAAE/G,SAAU;MACnB4C,YAAY,EAAEA,CAAA,KAAMvD,YAAY,CAACuC,OAAO,IAAI3B,YAAY,CAAC,KAAK;IAAE;MAAAwE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjE,CACF,eAEDtG,OAAA,CAACzB,KAAK;MACJkK,OAAO,EAAE5H,cAAe;MACxB6H,IAAI,EAAEvE,QAAS;MACfwE,QAAQ,EAAErE,YAAa;MACvBsE,QAAQ;MAAAlF,QAAA,gBAER1D,OAAA,CAACjC,cAAc;QACb8I,KAAK,EAAE;UAAEgC,QAAQ,EAAE,MAAM;UAAEC,KAAK,EAAE;QAAO,CAAE;QAC3CxH,KAAK,EAAC;MAAS;QAAA6E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChB,CAAC,eACFtG,OAAA;QAAM0G,SAAS,EAAC,MAAM;QAAAhD,QAAA,EAAEnD,CAAC,CAAC,YAAY;MAAC;QAAA4F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1C,CAAC;EAAA,eACR,CAAC;AAEP,CAAC;AAACjG,EAAA,CA1UID,OAAO;EAAA,QACGrB,cAAc,EACXjB,WAAW,EACPD,WAAW,EACfc,WAAW,EAG6BA,WAAW,EAI3CA,WAAW,EAIdA,WAAW,EAChBD,WAAW,EAETC,WAAW,EACZA,WAAW,EA8B7BmB,YAAY;AAAA;AAAAiJ,EAAA,GAjDR3I,OAAO;AA2Ub,eAAeA,OAAO;AAAC,IAAA2I,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}